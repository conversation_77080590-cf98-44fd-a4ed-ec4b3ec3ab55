import polars as pl
import sqlalchemy.orm
from sqlalchemy import and_, case, func, select

from backend.dao.postgres.constants import (CAPACITY_AGGREGATES,
                                            CAPACITY_COLUMN_ORDER,
                                            FCAS_SERVICES,
                                            GENERATION_COLUMN_ORDER,
                                            GENERATION_TECHNOLOGIES,
                                            TECH_CLASSIFICATION)
from backend.dao.postgres.tables import (AncillaryServices, HalfHourlyRegion,
                                         HalfHourlyRegionAncillary, Plants,
                                         PlantSettings, Regions, Scenarios,
                                         Technologies, Time,
                                         YearlyPlantOperations, YearlyRegion)
from backend.dao.postgres.utils import (fiscal_year_stmt,
                                        polaris_dataframe_from_sqlalchemy)


def get_scenario_id(session: sqlalchemy.orm.Session, scenario: str) -> int:
    """Get scenario ID from scenario name. Adds '-FYR' suffix if not present.

    Args:
        session: SQLAlchemy session
        scenario: Scenario name string

    Returns:
        int: Scenario ID

    Raises:
        ValueError: If scenario is not found in database
    """

    stmt = select(Scenarios.id).where(Scenarios.scenario == scenario)

    result = session.execute(stmt).scalar_one_or_none()

    return result


def query_twa_prices(
    session: sqlalchemy.orm.Session,
    url: sqlalchemy.engine.url,
    scenario_name: str,
    target_regions: list[str],
    first_year: int = 2025,
) -> pl.DataFrame:
    # Create first_date from first_year (fiscal year starts July 1st of previous calendar year)
    first_date = f"{first_year - 1}-07-01"

    stmt = (
        select(
            fiscal_year_stmt().label("fy"), YearlyRegion.baseloadprice, Regions.region
        )
        .join(Time, YearlyRegion.timeid == Time.id)
        .join(Regions, YearlyRegion.regionid == Regions.id)
        .join(Scenarios, YearlyRegion.scenarioid == Scenarios.id)
        .where(
            and_(
                Time.date >= first_date,
                Scenarios.scenario == scenario_name,
                Regions.region.in_(target_regions),
            )
        )
        .group_by(fiscal_year_stmt(), Regions.region, YearlyRegion.baseloadprice)
        .order_by(fiscal_year_stmt())
    )

    return polaris_dataframe_from_sqlalchemy(session, stmt, url)


def query_negative_prices(
    session: sqlalchemy.orm.Session,
    url: sqlalchemy.engine.url,
    scenario_name: str,
    target_regions: list[str],
    first_year: int = 2025,
) -> pl.DataFrame:
    """Query negative price frequency (<0) by region and fiscal year."""
    # Create first_date from first_year (fiscal year starts July 1st of previous calendar year)
    first_date = f"{first_year - 1}-07-01"

    stmt = (
        select(
            fiscal_year_stmt().label("fy"),
            Regions.region,
            func.count(case((HalfHourlyRegion.wholesaleprice < 0, 1))).label(
                "negative_count"
            ),
            func.count().label("total_count"),
        )
        .join(Time, HalfHourlyRegion.timeid == Time.id)
        .join(Regions, HalfHourlyRegion.regionid == Regions.id)
        .join(Scenarios, HalfHourlyRegion.scenarioid == Scenarios.id)
        .where(
            and_(
                Time.date >= first_date,
                Scenarios.scenario == scenario_name,
                Regions.region.in_(target_regions),
            )
        )
        .group_by(fiscal_year_stmt(), Regions.region)
        .order_by(fiscal_year_stmt(), Regions.region)
    )

    return polaris_dataframe_from_sqlalchemy(session, stmt, url)


def query_large_price_frequency(
    session: sqlalchemy.orm.Session,
    url: sqlalchemy.engine.url,
    scenario_name: str,
    target_regions: list[str],
    price_threshold: float = 300,
    first_year: int = 2025,
) -> pl.DataFrame:
    """Query frequency of prices above threshold (default >$300) by region and fiscal year."""
    # Create first_date from first_year (fiscal year starts July 1st of previous calendar year)
    first_date = f"{first_year - 1}-07-01"

    stmt = (
        select(
            fiscal_year_stmt().label("fy"),
            Regions.region,
            func.count(
                case((HalfHourlyRegion.wholesaleprice > price_threshold, 1))
            ).label("great_300"),
            func.count().label("total_count"),
        )
        .join(Time, HalfHourlyRegion.timeid == Time.id)
        .join(Regions, HalfHourlyRegion.regionid == Regions.id)
        .join(Scenarios, HalfHourlyRegion.scenarioid == Scenarios.id)
        .where(
            and_(
                Time.date >= first_date,
                Scenarios.scenario == scenario_name,
                Regions.region.in_(target_regions),
            )
        )
        .group_by(fiscal_year_stmt(), Regions.region)
        .order_by(fiscal_year_stmt(), Regions.region)
    )

    return polaris_dataframe_from_sqlalchemy(session, stmt, url)


def query_fcas_prices_per_region(
    session: sqlalchemy.orm.Session,
    url: sqlalchemy.engine.url,
    scenario_name: str,
    region: str,
    services: list[str] = None,
    first_year: int = 2025,
) -> pl.DataFrame:
    """Query FCAS prices per region and service."""
    if services is None:
        services = FCAS_SERVICES

    # Create first_date from first_year (fiscal year starts July 1st of previous calendar year)
    first_date = f"{first_year - 1}-07-01"

    stmt = (
        select(
            fiscal_year_stmt().label("fy"),
            AncillaryServices.service,
            func.avg(HalfHourlyRegionAncillary.clearingpricepermwperh).label("price"),
        )
        .join(Time, HalfHourlyRegionAncillary.timeid == Time.id)
        .join(
            AncillaryServices,
            HalfHourlyRegionAncillary.serviceid == AncillaryServices.id,
        )
        .join(Regions, HalfHourlyRegionAncillary.regionid == Regions.id)
        .join(Scenarios, HalfHourlyRegionAncillary.scenarioid == Scenarios.id)
        .where(
            and_(
                Time.date >= first_date,
                Scenarios.scenario == scenario_name,
                Regions.region == region,
                AncillaryServices.service.in_(services),
            )
        )
        .group_by(fiscal_year_stmt(), AncillaryServices.service)
        .order_by(fiscal_year_stmt())
    )

    return polaris_dataframe_from_sqlalchemy(session, stmt, url)


def query_nem_capacity(
    session: sqlalchemy.orm.Session,
    url: sqlalchemy.engine.url,
    scenario_name: str,
    filtered_years: list[int] = None,
) -> pl.DataFrame:
    """Query NEM capacity by technology and fiscal year with technology mapping and aggregations."""
    stmt = (
        select(
            fiscal_year_stmt().label("fy"),
            Technologies.technology,
            func.sum(YearlyPlantOperations.capacity).label("capacity"),
        )
        .join(Time, YearlyPlantOperations.timeid == Time.id)
        .join(Plants, YearlyPlantOperations.plantid == Plants.id)
        .join(Scenarios, YearlyPlantOperations.scenarioid == Scenarios.id)
        .join(Technologies, Plants.technologyid == Technologies.id)
        .join(Regions, Plants.regionid == Regions.id)
        .where(
            and_(
                Scenarios.scenario == scenario_name,
                ~Technologies.technology.in_(["evs", "evx"]),
                fiscal_year_stmt().in_(filtered_years) if filtered_years else True,
            )
        )
        .group_by(fiscal_year_stmt(), Technologies.technology)
        .order_by(fiscal_year_stmt(), Technologies.technology)
    )

    # Get raw data
    raw_df = polaris_dataframe_from_sqlalchemy(session, stmt, url)

    # Apply technology classification and create expanded dataframe
    expanded_rows = []
    for row in raw_df.iter_rows(named=True):
        tech_group = TECH_CLASSIFICATION.get(row["technology"], "Other")
        # Skip empty classifications
        if tech_group:
            expanded_rows.append(
                {
                    "fy": row["fy"],
                    "technology": row["technology"],
                    "tech_group": tech_group,
                    "capacity": row["capacity"],
                }
            )

    if not expanded_rows:
        # Return empty dataframe with expected structure
        return pl.DataFrame(
            schema={
                "fy": pl.Int64,
                **{col: pl.Float64 for col in CAPACITY_COLUMN_ORDER[1:]},
            }
        )

    expanded_df = pl.DataFrame(expanded_rows)

    # Pivot and aggregate by tech_group
    capacity_df = (
        expanded_df.group_by(["fy", "tech_group"])
        .agg([pl.col("capacity").sum()])
        .pivot(index="fy", on="tech_group", values="capacity")
        .fill_null(0)
    )

    # Ensure all required columns exist
    for col in CAPACITY_COLUMN_ORDER[1:]:  # Skip 'fy' column
        if col not in capacity_df.columns:
            capacity_df = capacity_df.with_columns(pl.lit(0.0).alias(col))

    # Add aggregated columns
    for name, components in CAPACITY_AGGREGATES.items():
        existing_components = [c for c in components if c in capacity_df.columns]
        if existing_components:
            capacity_df = capacity_df.with_columns(
                pl.sum_horizontal([pl.col(c) for c in existing_components]).alias(name)
            )
        else:
            capacity_df = capacity_df.with_columns(pl.lit(0.0).alias(name))

    # Add dispatchable column
    capacity_df = capacity_df.with_columns(
        (pl.col("baseload") + pl.col("flexible")).alias("dispatchable").round(0)
    )

    # Reorder columns
    final_columns = CAPACITY_COLUMN_ORDER + [
        c for c in capacity_df.columns if c not in CAPACITY_COLUMN_ORDER
    ]
    capacity_df = capacity_df.select(final_columns)

    # Sort by fiscal year for consistent output
    capacity_df = capacity_df.sort("fy")

    return capacity_df


def query_battery_spreads(
    session: sqlalchemy.orm.Session,
    url: sqlalchemy.engine.url,
    scenario_name: str,
    target_regions: list[str],
    duration_hours: int,
    first_year: int = 2025,
) -> pl.DataFrame:
    """Query battery spreads for a specific duration by region and fiscal year."""
    # Create first_date from first_year (fiscal year starts July 1st of previous calendar year)
    first_date = f"{first_year - 1}-07-01"

    spread_column = getattr(YearlyRegion, f"averagedailyspreadprice{duration_hours}h")

    stmt = (
        select(
            fiscal_year_stmt().label("fy"),
            Regions.region,
            spread_column.label("spread"),
        )
        .join(Time, YearlyRegion.timeid == Time.id)
        .join(Regions, YearlyRegion.regionid == Regions.id)
        .join(Scenarios, YearlyRegion.scenarioid == Scenarios.id)
        .where(
            and_(
                Time.date >= first_date,
                Scenarios.scenario == scenario_name,
                Regions.region.in_(target_regions),
            )
        )
        .group_by(fiscal_year_stmt(), Regions.region, spread_column)
        .order_by(fiscal_year_stmt(), Regions.region)
    )

    return polaris_dataframe_from_sqlalchemy(session, stmt, url)


def query_state_and_rez_capacity_buildout(
    session: sqlalchemy.orm.Session,
    url: sqlalchemy.engine.url,
    scenario_name: str,
    region: str = None,
    rez: str = None,
    filtered_years: list[int] = None,
) -> pl.DataFrame:
    """Query state or REZ capacity buildout by technology and fiscal year."""
    stmt = (
        select(
            fiscal_year_stmt().label("fy"),
            Technologies.technology,
            func.sum(YearlyPlantOperations.capacity).label("capacity"),
        )
        .join(Time, YearlyPlantOperations.timeid == Time.id)
        .join(Plants, YearlyPlantOperations.plantid == Plants.id)
        .join(Scenarios, YearlyPlantOperations.scenarioid == Scenarios.id)
        .join(Technologies, Plants.technologyid == Technologies.id)
        .join(Regions, Plants.regionid == Regions.id)
    )

    # Add PlantSettings join if REZ is specified
    if rez:
        stmt = stmt.join(
            PlantSettings,
            and_(
                PlantSettings.plantid == Plants.id,
                PlantSettings.scenarioid == Scenarios.id,
            ),
        )

    # Build where conditions
    where_conditions = [
        Scenarios.scenario == scenario_name,
        ~Technologies.technology.in_(["evs", "evx"]),
    ]

    if filtered_years and not rez:
        where_conditions.append(fiscal_year_stmt().in_(filtered_years))

    if region:
        where_conditions.append(Regions.region == region)

    if rez:
        where_conditions.append(PlantSettings.subregion == rez)

    stmt = stmt.where(and_(*where_conditions))
    stmt = stmt.group_by(fiscal_year_stmt(), Technologies.technology)
    stmt = stmt.order_by(fiscal_year_stmt(), Technologies.technology)

    # Get raw data
    raw_df = polaris_dataframe_from_sqlalchemy(session, stmt, url)

    # Apply technology classification and create expanded dataframe
    expanded_rows = []
    for row in raw_df.iter_rows(named=True):
        tech_group = TECH_CLASSIFICATION.get(row["technology"], "Other")
        # Skip empty classifications
        if tech_group:
            expanded_rows.append(
                {
                    "fy": row["fy"],
                    "technology": row["technology"],
                    "tech_group": tech_group,
                    "capacity": row["capacity"],
                }
            )

    if not expanded_rows:
        # Return empty dataframe with expected structure
        return pl.DataFrame(
            schema={
                "fy": pl.Int64,
                **{col: pl.Float64 for col in CAPACITY_COLUMN_ORDER[1:]},
            }
        )

    expanded_df = pl.DataFrame(expanded_rows)

    # Pivot and aggregate by tech_group
    capacity_df = (
        expanded_df.group_by(["fy", "tech_group"])
        .agg([pl.col("capacity").sum()])
        .pivot(index="fy", on="tech_group", values="capacity")
        .fill_null(0)
    )

    # Ensure all required columns exist
    for col in CAPACITY_COLUMN_ORDER[1:]:  # Skip 'fy' column
        if col not in capacity_df.columns:
            capacity_df = capacity_df.with_columns(pl.lit(0.0).alias(col))

    # Add aggregated columns
    for name, components in CAPACITY_AGGREGATES.items():
        existing_components = [c for c in components if c in capacity_df.columns]
        if existing_components:
            capacity_df = capacity_df.with_columns(
                pl.sum_horizontal([pl.col(c) for c in existing_components]).alias(name)
            )
        else:
            capacity_df = capacity_df.with_columns(pl.lit(0.0).alias(name))

    # Add dispatchable column
    capacity_df = capacity_df.with_columns(
        (pl.col("baseload") + pl.col("flexible")).alias("dispatchable")
    )

    # Reorder columns
    final_columns = CAPACITY_COLUMN_ORDER + [
        c for c in capacity_df.columns if c not in CAPACITY_COLUMN_ORDER
    ]
    capacity_df = capacity_df.select(final_columns)

    # Sort by fiscal year for consistent output
    capacity_df = capacity_df.sort("fy")

    return capacity_df


def query_nem_production_raw(
    session: sqlalchemy.orm.Session,
    url: sqlalchemy.engine.url,
    scenario_name: str,
    filtered_years: list[int] = None,
) -> pl.DataFrame:
    """Query raw NEM generation data matching deck_builder_2.py query 10 exactly."""
    # This matches the exact SQL from Deck_builder_2.py lines 409-425
    stmt = (
        select(
            fiscal_year_stmt().label("fy"),
            Technologies.technology,
            func.sum(YearlyPlantOperations.grossproductionintwh).label("generation"),
            (
                func.sum(YearlyPlantOperations.netproductionintwh)
                - func.sum(YearlyPlantOperations.grossproductionintwh)
            ).label("load"),
        )
        .join(Time, YearlyPlantOperations.timeid == Time.id)
        .join(Plants, YearlyPlantOperations.plantid == Plants.id)
        .join(Scenarios, YearlyPlantOperations.scenarioid == Scenarios.id)
        .join(Technologies, Plants.technologyid == Technologies.id)
        .join(Regions, Plants.regionid == Regions.id)
        .where(
            and_(
                Scenarios.scenario == scenario_name,
                ~Technologies.technology.in_(["evs", "evx"]),
                fiscal_year_stmt().in_(filtered_years) if filtered_years else True,
            )
        )
        .group_by(fiscal_year_stmt(), Technologies.technology)
        .order_by(fiscal_year_stmt(), Technologies.technology)
    )

    return polaris_dataframe_from_sqlalchemy(session, stmt, url)


def query_nem_production(
    session: sqlalchemy.orm.Session,
    url: sqlalchemy.engine.url,
    scenario_name: str,
    filtered_years: list[int] = None,
) -> pl.DataFrame:
    """Process NEM generation data exactly like Deck_builder_2.py query 10."""
    try:
        # Get raw data
        nem_gen_raw = query_nem_production_raw(
            session, url, scenario_name, filtered_years
        )

        # Function to map technology codes to group names using unified TECH_CLASSIFICATION
        def map_tech_to_group(tech_code):
            return TECH_CLASSIFICATION.get(tech_code, "Other")

        # Apply the mapping function to create a new 'tech_group' column
        nem_gen_raw = nem_gen_raw.with_columns(
            pl.col("technology")
            .map_elements(map_tech_to_group, return_dtype=pl.Utf8)
            .alias("tech_group")
        )

        # Filter the data to only include rows where tech_group is in the list of technologies to process
        nem_gen_raw = nem_gen_raw.filter(
            pl.col("tech_group").is_in(GENERATION_TECHNOLOGIES)
        )

        # Aggregate (sum) generation and load by financial year and technology group
        agg_nem_gen_df = nem_gen_raw.group_by(["fy", "tech_group"]).agg(
            [pl.col("generation").sum(), pl.col("load").sum()]
        )

        # Create pivot table for generation data only (no load columns)
        nem_gen_df_merged = agg_nem_gen_df.pivot(
            index="fy", columns="tech_group", values="generation"
        ).fill_null(0)

        # Reorder columns to match the generation order specified (Deck_builder_2.py line 484)
        available_columns = [
            col for col in GENERATION_COLUMN_ORDER if col in nem_gen_df_merged.columns
        ]
        if available_columns:
            nem_gen_df_merged = nem_gen_df_merged.select(available_columns)

        # Sort by fiscal year for consistent output
        nem_gen_df_merged = nem_gen_df_merged.sort("fy")

        return nem_gen_df_merged

    except Exception as e:
        print(f"Error in generation processing: {e}")
        import traceback

        traceback.print_exc()
        # Fall back to raw data if processing fails
        return query_nem_production_raw(session, url, scenario_name, filtered_years)


def query_state_production_raw(
    session: sqlalchemy.orm.Session,
    url: sqlalchemy.engine.url,
    scenario_name: str,
    region: str = None,
    filtered_years: list[int] = None,
) -> pl.DataFrame:
    """Query raw state generation data matching deck_builder_2.py query 11."""
    stmt = (
        select(
            fiscal_year_stmt().label("fy"),
            Technologies.technology,
            func.sum(YearlyPlantOperations.grossproductionintwh).label("generation"),
            (
                func.sum(YearlyPlantOperations.netproductionintwh)
                - func.sum(YearlyPlantOperations.grossproductionintwh)
            ).label("load"),
        )
        .join(Time, YearlyPlantOperations.timeid == Time.id)
        .join(Plants, YearlyPlantOperations.plantid == Plants.id)
        .join(Scenarios, YearlyPlantOperations.scenarioid == Scenarios.id)
        .join(Technologies, Plants.technologyid == Technologies.id)
        .join(Regions, Plants.regionid == Regions.id)
    )

    where_conditions = [
        Scenarios.scenario == scenario_name,
        ~Technologies.technology.in_(["evs", "evx"]),
    ]
    if filtered_years:
        where_conditions.append(fiscal_year_stmt().in_(filtered_years))
    if region:
        where_conditions.append(Regions.region == region)

    stmt = stmt.where(and_(*where_conditions))
    stmt = stmt.group_by(fiscal_year_stmt(), Technologies.technology)
    stmt = stmt.order_by(fiscal_year_stmt(), Technologies.technology)

    return polaris_dataframe_from_sqlalchemy(session, stmt, url)


def query_state_production(
    session: sqlalchemy.orm.Session,
    url: sqlalchemy.engine.url,
    scenario_name: str,
    region: str = None,
    filtered_years: list[int] = None,
) -> pl.DataFrame:
    """Process state generation data exactly like Deck_builder_2.py query 11."""
    try:
        # Get raw data
        state_gen_raw = query_state_production_raw(
            session, url, scenario_name, region, filtered_years
        )

        # Function to map technology codes to group names using unified TECH_CLASSIFICATION
        def map_tech_to_group(tech_code):
            return TECH_CLASSIFICATION.get(tech_code, "Other")

        # Apply the mapping function to create a new 'tech_group' column
        state_gen_raw = state_gen_raw.with_columns(
            pl.col("technology")
            .map_elements(map_tech_to_group, return_dtype=pl.Utf8)
            .alias("tech_group")
        )

        # Filter the data to only include rows where tech_group is in the list of technologies to process
        state_gen_raw = state_gen_raw.filter(
            pl.col("tech_group").is_in(GENERATION_TECHNOLOGIES)
        )

        # Aggregate (sum) generation and load by financial year and technology group
        agg_state_gen_df = state_gen_raw.group_by(["fy", "tech_group"]).agg(
            [pl.col("generation").sum(), pl.col("load").sum()]
        )

        # Create pivot table for generation data only (no load columns)
        state_gen_df_merged = agg_state_gen_df.pivot(
            index="fy", columns="tech_group", values="generation"
        ).fill_null(0)

        # Reorder columns to match the generation order specified (Deck_builder_2.py line 562)
        available_columns = [
            col for col in GENERATION_COLUMN_ORDER if col in state_gen_df_merged.columns
        ]
        if available_columns:
            state_gen_df_merged = state_gen_df_merged.select(available_columns)

        # Sort by fiscal year for consistent output
        state_gen_df_merged = state_gen_df_merged.sort("fy")

        return state_gen_df_merged

    except Exception as e:
        print(f"Error in state generation processing: {e}")
        import traceback

        traceback.print_exc()
        # Fall back to raw data if processing fails
        return query_state_production_raw(
            session, url, scenario_name, region, filtered_years
        )
