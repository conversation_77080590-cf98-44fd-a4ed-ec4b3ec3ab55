import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from PIL import Image, ImageTk
from .constants import (
    AURORA_COLORS, AVAILABLE_STATES, AVA<PERSON>ABLE_DURATIONS, DEFAULT_SCENARIO_NAME,
    WINDOW_GEOMETRY, WINDOW_TITLE, HEADER_HEIGHT, HEADER_TITLE, LOGO_SIZE, LOGO_PATH,
    MAIN_PADDING, HEADER_FONT, LABEL_FONT, REGULAR_FONT, SMALL_FONT,
    ENTRY_WIDTH, REZ_ENTRY_WIDTH, CHECKBOX_PADDING_X, CHECKBOX_PADDING_Y,
    BUTTON_PADDING_X, SECTION_PADDING_Y, CONFIG_FILE_NAME, ERROR_MESSAGES, SUCCESS_MESSAGES,
    STATE_REZS, AVAILABLE_FIRST_YEARS, DEFAULT_FIRST_YEAR
)


class ConfigUI:
    def __init__(self, root):
        self.root = root
        self.root.title(WINDOW_TITLE)
        self.root.geometry(WINDOW_GEOMETRY)

        # Aurora Energy Research color scheme
        self.colors = AURORA_COLORS
        
        # Configure root window
        self.root.configure(bg=self.colors['background'])
        
        # Configure ttk styles with Aurora theme
        self.setup_styles()

        # Set Aurora logo as application icon
        self.set_app_icon()

        # Variables to store inputs
        self.scenario_name = tk.StringVar(value=DEFAULT_SCENARIO_NAME)
        self.first_year = tk.IntVar(value=DEFAULT_FIRST_YEAR)

        # State checkboxes - Australian states (NEM regions)
        self.available_states = AVAILABLE_STATES
        self.state_vars = {}
        for state in self.available_states:
            self.state_vars[state] = tk.BooleanVar(value=False)

        # Battery duration checkboxes
        self.available_durations = AVAILABLE_DURATIONS
        self.duration_vars = {}
        for duration in self.available_durations:
            self.duration_vars[duration] = tk.BooleanVar(value=False)

        # REZ functionality enable/disable checkbox
        self.enable_rez = tk.BooleanVar(value=False)

        # REZ checkboxes for each state (all unchecked by default)
        self.rez_vars = {}
        for state in self.available_states:
            self.rez_vars[state] = {}
            for rez in STATE_REZS.get(state, []):
                self.rez_vars[state][rez] = tk.BooleanVar(value=False)

        self.create_widgets()
    
    def setup_styles(self):
        """Configure ttk styles with Aurora Energy Research theme."""
        style = ttk.Style()
        
        # Configure main frame style
        style.configure('Aurora.TFrame', 
                       background=self.colors['surface'],
                       relief='flat',
                       borderwidth=1)
        
        # Configure label styles
        style.configure('Aurora.TLabel',
                       background=self.colors['surface'],
                       foreground=self.colors['text'],
                       font=REGULAR_FONT)

        style.configure('AuroraTitle.TLabel',
                       background=self.colors['surface'],
                       foreground=self.colors['primary'],
                       font=('Segoe UI', 12, 'bold'))

        style.configure('AuroraHeader.TLabel',
                       background=self.colors['surface'],
                       foreground=self.colors['primary'],
                       font=LABEL_FONT)
        
        # Configure button styles with simple gray colors
        style.configure('Aurora.TButton',
                       background='#495057',  # Darker gray for better visibility
                       foreground='white',
                       font=REGULAR_FONT,
                       padding=(20, 8))

        style.map('Aurora.TButton',
                 background=[('active', '#6C757D'),
                           ('pressed', '#343A40')])

        style.configure('AuroraSecondary.TButton',
                       background='#ADB5BD',  # Lighter gray
                       foreground='black',
                       font=SMALL_FONT,
                       padding=(15, 6))

        style.map('AuroraSecondary.TButton',
                 background=[('active', '#9CA3AF'),
                           ('pressed', '#8B92A5')])
        
        # Configure entry and combobox styles
        style.configure('Aurora.TEntry',
                       fieldbackground='white',
                       borderwidth=1,
                       relief='solid',
                       font=REGULAR_FONT)

        style.configure('Aurora.TCombobox',
                       fieldbackground='white',
                       borderwidth=1,
                       relief='solid',
                       font=REGULAR_FONT)

        # Configure checkbutton style
        style.configure('Aurora.TCheckbutton',
                       background=self.colors['surface'],
                       foreground=self.colors['text'],
                       font=REGULAR_FONT,
                       focuscolor='none')

        # Configure labelframe style
        style.configure('Aurora.TLabelframe',
                       background=self.colors['surface'],
                       foreground=self.colors['primary'],
                       font=REGULAR_FONT + ('bold',),
                       borderwidth=2,
                       relief='groove')

        style.configure('Aurora.TLabelframe.Label',
                       background=self.colors['surface'],
                       foreground=self.colors['primary'],
                       font=REGULAR_FONT + ('bold',))

    def set_app_icon(self):
        """Set the Aurora logo as the application icon."""
        try:
            if os.path.exists(LOGO_PATH):
                # Load and resize logo for icon
                image = Image.open(LOGO_PATH)
                # Resize to icon size
                icon_image = image.resize(LOGO_SIZE, Image.Resampling.LANCZOS)
                icon_photo = ImageTk.PhotoImage(icon_image)

                # Set as window icon
                self.root.iconphoto(True, icon_photo)
                # Keep reference to prevent garbage collection
                self.root.icon_image = icon_photo
                return True
            else:
                # Fallback: set window title to indicate Aurora branding
                self.root.title(WINDOW_TITLE)
                return False
        except Exception as e:
            print(ERROR_MESSAGES['icon_failed'].format(error=e))
            # Fallback: set window title to indicate Aurora branding
            self.root.title(WINDOW_TITLE)
            return False

    def create_widgets(self):
        # Header frame with title only
        header_frame = tk.Frame(self.root, bg=self.colors['primary'], height=HEADER_HEIGHT)
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=0, pady=0)
        header_frame.grid_propagate(False)

        # Title in header (centered)
        title_label = tk.Label(
            header_frame,
            text=HEADER_TITLE,
            bg=self.colors['primary'],
            fg='white',
            font=HEADER_FONT
        )
        title_label.pack(expand=True)

        # Main content frame
        main_frame = ttk.Frame(self.root, style='Aurora.TFrame', padding=MAIN_PADDING)
        main_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(1, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Scenario Name
        ttk.Label(main_frame, text="Scenario Name:", style='AuroraHeader.TLabel').grid(row=0, column=0, sticky=tk.W, pady=(0, SECTION_PADDING_Y))
        scenario_entry = ttk.Entry(main_frame, textvariable=self.scenario_name, width=ENTRY_WIDTH, style='Aurora.TEntry')
        scenario_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, SECTION_PADDING_Y), padx=(SECTION_PADDING_Y, 0))

        # First Year Selection
        ttk.Label(main_frame, text="First Year:", style='AuroraHeader.TLabel').grid(row=1, column=0, sticky=tk.W, pady=(SECTION_PADDING_Y, 5))
        first_year_combo = ttk.Combobox(main_frame, textvariable=self.first_year, values=AVAILABLE_FIRST_YEARS,
                                       state="readonly", width=20, style='Aurora.TCombobox')
        first_year_combo.grid(row=1, column=1, sticky=tk.W, pady=(SECTION_PADDING_Y, 5), padx=(SECTION_PADDING_Y, 0))

        # Target Regions (Checkboxes)
        ttk.Label(main_frame, text="Target Regions:", style='AuroraHeader.TLabel').grid(row=2, column=0, sticky=tk.W, pady=(SECTION_PADDING_Y, 5))

        # Create frame for checkboxes
        regions_frame = ttk.Frame(main_frame, style='Aurora.TFrame')
        regions_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(SECTION_PADDING_Y, 0))

        # Create checkboxes in a single row (5 states)
        for i, state in enumerate(self.available_states):
            cb = ttk.Checkbutton(
                regions_frame,
                text=state,
                variable=self.state_vars[state],
                command=self.on_region_change,
                style='Aurora.TCheckbutton'
            )
            cb.grid(row=0, column=i, sticky=tk.W, padx=(0, CHECKBOX_PADDING_X), pady=CHECKBOX_PADDING_Y)

        # Battery Durations (Checkboxes)
        ttk.Label(main_frame, text="Battery Durations (hours):", style='AuroraHeader.TLabel').grid(row=3, column=0, sticky=tk.W, pady=(20, 5))

        # Create frame for duration checkboxes
        durations_frame = ttk.Frame(main_frame, style='Aurora.TFrame')
        durations_frame.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(15, 0))

        # Create checkboxes for durations in a single row
        for i, duration in enumerate(self.available_durations):
            cb = ttk.Checkbutton(
                durations_frame,
                text=f"{duration}h",
                variable=self.duration_vars[duration],
                style='Aurora.TCheckbutton'
            )
            cb.grid(row=0, column=i, sticky=tk.W, padx=(0, 20), pady=5)

        # REZ Enable/Disable Checkbox
        ttk.Label(main_frame, text="REZ Configuration:", style='AuroraHeader.TLabel').grid(row=4, column=0, sticky=tk.W, pady=(25, 5))

        # Create frame for REZ enable checkbox
        rez_enable_frame = ttk.Frame(main_frame, style='Aurora.TFrame')
        rez_enable_frame.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=(25, 5), padx=(SECTION_PADDING_Y, 0))

        # REZ enable checkbox
        rez_enable_cb = ttk.Checkbutton(
            rez_enable_frame,
            text="Enable REZ selection",
            variable=self.enable_rez,
            command=self.on_rez_enable_change,
            style='Aurora.TCheckbutton'
        )
        rez_enable_cb.grid(row=0, column=0, sticky=tk.W)

        # REZ Frame (initially hidden)
        self.rez_frame = ttk.LabelFrame(main_frame, text="Region REZ Mappings", padding="15", style='Aurora.TLabelframe')
        self.rez_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        self.rez_frame.columnconfigure(1, weight=1)
        self.rez_frame.grid_remove()  # Hide initially

        # REZ input area
        self.rez_inputs_frame = ttk.Frame(self.rez_frame, style='Aurora.TFrame')
        self.rez_inputs_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E))

        # Buttons frame with better spacing
        buttons_frame = ttk.Frame(main_frame, style='Aurora.TFrame')
        buttons_frame.grid(row=6, column=0, columnspan=2, pady=(30, 20))

        # Center the buttons
        buttons_frame.grid_columnconfigure(0, weight=1)
        buttons_frame.grid_columnconfigure(3, weight=1)

        ttk.Button(buttons_frame, text="Run Analysis", command=self.run_analysis, style='AuroraSecondary.TButton').grid(row=0, column=1, padx=10)
        ttk.Button(buttons_frame, text="Exit", command=self.root.quit, style='AuroraSecondary.TButton').grid(row=0, column=2, padx=10)

        # Initialize REZ fields
        self.update_rez_fields()

    def on_region_change(self):
        """Called when region selection changes."""
        self.update_rez_fields()

    def on_rez_enable_change(self):
        """Called when REZ enable checkbox changes."""
        if self.enable_rez.get():
            # Show REZ frame and update fields
            self.rez_frame.grid()
            self.update_rez_fields()
        else:
            # Hide REZ frame and clear all REZ selections
            self.rez_frame.grid_remove()
            self.clear_rez_selections()

    def get_selected_regions(self):
        """Get list of selected regions."""
        return [state for state, var in self.state_vars.items() if var.get()]

    def get_selected_durations(self):
        """Get list of selected battery durations."""
        return [duration for duration, var in self.duration_vars.items() if var.get()]

    def clear_rez_selections(self):
        """Clear all REZ selections."""
        for state in self.rez_vars:
            for rez_var in self.rez_vars[state].values():
                rez_var.set(False)

    def update_rez_fields(self):
        """Update REZ checkboxes based on selected regions."""
        # Only update if REZ functionality is enabled
        if not self.enable_rez.get():
            return

        # Clear existing REZ inputs
        for widget in self.rez_inputs_frame.winfo_children():
            widget.destroy()

        # Get selected regions from checkboxes
        regions = self.get_selected_regions()

        # No regions selected
        if not regions:
            ttk.Label(self.rez_inputs_frame, text="Select regions to see available REZs", style='Aurora.TLabel').grid(
                row=0, column=0, sticky=tk.W, pady=8)
            return

        # Create REZ checkboxes for each selected region
        current_row = 0
        for region in regions:
            # Region header
            ttk.Label(self.rez_inputs_frame, text=f"{region} REZs:", style='Aurora.TLabel').grid(
                row=current_row, column=0, sticky=tk.W, pady=(15, 5), padx=(0, 10))
            current_row += 1

            # Create frame for REZ checkboxes
            rez_frame = ttk.Frame(self.rez_inputs_frame, style='Aurora.TFrame')
            rez_frame.grid(row=current_row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
            current_row += 1

            # Create checkboxes for REZs in a grid layout (3 columns)
            rez_options = STATE_REZS.get(region, [])
            if not rez_options:
                ttk.Label(rez_frame, text="No REZs available for this region", style='Aurora.TLabel').grid(
                    row=0, column=0, sticky=tk.W, pady=5)
                continue

            for i, rez in enumerate(rez_options):
                row = i // 3
                col = i % 3

                # Create checkbox if it doesn't exist
                if rez not in self.rez_vars[region]:
                    self.rez_vars[region][rez] = tk.BooleanVar(value=False)

                cb = ttk.Checkbutton(
                    rez_frame,
                    text=rez,
                    variable=self.rez_vars[region][rez],
                    style='Aurora.TCheckbutton'
                )
                cb.grid(row=row, column=col, sticky=tk.W, padx=(0, CHECKBOX_PADDING_X), pady=CHECKBOX_PADDING_Y)

        self.rez_inputs_frame.columnconfigure(1, weight=1)

    def get_config(self):
        """Get current configuration from UI."""
        # Get selected REZs from checkboxes (only if REZ functionality is enabled)
        region_rezs = {}
        if self.enable_rez.get():
            for region in self.get_selected_regions():
                selected_rezs = []
                for rez, var in self.rez_vars.get(region, {}).items():
                    if var.get():  # If checkbox is selected
                        selected_rezs.append(rez)
                if selected_rezs:
                    region_rezs[region] = selected_rezs

        return {
            "SCENARIO_NAME": self.scenario_name.get().strip(),
            "TARGET_REGIONS": self.get_selected_regions(),
            "BATTERY_DURATIONS": self.get_selected_durations(),
            "REGION_REZS": region_rezs,
            "FIRST_YEAR": self.first_year.get()
        }

    def run_analysis(self):
        """Run the main analysis with current configuration."""
        try:
            config = self.get_config()

            # Validate inputs
            if not config["SCENARIO_NAME"]:
                messagebox.showerror("Error", ERROR_MESSAGES['no_scenario'])
                return

            if not config["TARGET_REGIONS"]:
                messagebox.showerror("Error", ERROR_MESSAGES['no_regions'])
                return

            # Battery durations are optional - no validation needed

            # Save config for main.py to use
            with open(CONFIG_FILE_NAME, "w") as f:
                json.dump(config, f, indent=2)

            # Import and run main with the configuration
            import sys
            import os
            # Add the project root to the path
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            sys.path.insert(0, project_root)
            from backend.main import run_with_config
            run_with_config(config)

            messagebox.showinfo("Success", SUCCESS_MESSAGES['analysis_complete'])

        except Exception as e:
            messagebox.showerror("Error", ERROR_MESSAGES['analysis_failed'].format(error=str(e)))


def main():
    root = tk.Tk()
    app = ConfigUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
