#!/usr/bin/env python3
"""
Aurora Deck Builder

"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        from frontend.components.config_ui import main as launch_ui
        launch_ui()
    except ImportError as e:
        print("Error: Missing required dependencies")
        sys.exit(1)
    except Exception as e:
        print(f"Application error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
