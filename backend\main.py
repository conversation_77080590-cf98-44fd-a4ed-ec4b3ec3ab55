import logging
import os
import sys

import polars as pl

from backend.dao.postgres.connect import connect
from backend.dao.postgres.queries import (
    get_scenario_id, query_battery_spreads, query_fcas_prices_per_region,
    query_large_price_frequency, query_negative_prices, query_nem_capacity,
    query_nem_production, query_state_and_rez_capacity_buildout,
    query_state_production, query_twa_prices)
from utils import set_up_logger

sys.path.append("..")

logger = logging.getLogger(__name__)
set_up_logger(logger)

# TODO - get the logger to work


def load_config():
    """Load configuration from JSON file."""
    import json

    try:
        if os.path.exists("deck_builder_config.json"):
            with open("deck_builder_config.json", "r") as f:
                return json.load(f)
    except Exception as e:
        print(f"Warning: Could not load config file: {e}")


def run_with_config(config=None):
    """Run the analysis with the provided configuration."""
    if config is None:
        config = load_config()

    # Extract configuration values
    SCENARIO_NAME = config["SCENARIO_NAME"]
    TARGET_REGIONS = config["TARGET_REGIONS"]
    BATTERY_DURATIONS = config["BATTERY_DURATIONS"]
    REGION_REZS = config["REGION_REZS"]
    FIRST_YEAR = config.get("FIRST_YEAR", 2025)  # Default to 2025 if not provided

    # Create filtered years list: first year + increments on 5-year basis from 2030
    FILTERED_YEARS = [FIRST_YEAR]
    if FIRST_YEAR < 2030:
        # FILTERED_YEARS.extend(
        #     range(2030, 2061, 5)
        # )  # 2030, 2035, 2040, 2045, 2050, 2055, 2060
        FILTERED_YEARS.extend(range(FIRST_YEAR + 1, 2061, 1))
    else:
        # If first year is 2030 or later, start from first year and increment by 5
#        FILTERED_YEARS.extend(range(FIRST_YEAR + 5, 2061, 1))
         FILTERED_YEARS.extend(range(FIRST_YEAR + 1, 2061, 1))

    print(f"Using filtered years for capacity and generation queries: {FILTERED_YEARS}")
    print(f"Target regions: {TARGET_REGIONS}")
    print(f"Battery durations: {BATTERY_DURATIONS}")
    print(f"Region REZs: {REGION_REZS}")

    session, url = connect()

    # Fix scenario in case '-FYR' is missing
    if not SCENARIO_NAME.endswith("-FYR"):
        SCENARIO_NAME = SCENARIO_NAME + "-FYR"

    scenario_id = get_scenario_id(session, SCENARIO_NAME)

    if scenario_id is None:
        raise ValueError(f"Scenario '{SCENARIO_NAME}' not found in database")

    # Create output directory constant
    OUTPUT_BASE_DIR = "outputs"

    OUTPUT_DIR = os.path.join(OUTPUT_BASE_DIR, SCENARIO_NAME)
#    OUTPUT_DIR = os.path.join(OUTPUT_BASE_DIR, "Case 2")
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    print(f"Processing scenario: {SCENARIO_NAME}")

    # 1) TWA Yearly Prices
    print("Running TWA prices query...")
    twa_df = query_twa_prices(session, url, SCENARIO_NAME, TARGET_REGIONS, FIRST_YEAR)

    # Create the combined TWA file with all regions
    twa_pivot = twa_df.pivot(index="fy", on="region", values="baseloadprice")
    twa_pivot.write_csv(os.path.join(OUTPUT_DIR, "TWA.csv"), include_header=True)

    # 2) Negative Price Frequency
    print("Running negative prices query...")
    neg_df = query_negative_prices(
        session, url, SCENARIO_NAME, TARGET_REGIONS, FIRST_YEAR
    )
    # Calculate percentage
    neg_df = neg_df.with_columns(
        (pl.col("negative_count") * 100.0 / pl.col("total_count")).alias("pct%")
    )

    # Create the combined file with all regions
    neg_pivot = neg_df.pivot(index="fy", on="region", values="pct%")
    neg_pivot.write_csv(
        os.path.join(OUTPUT_DIR, "Negative_Prices.csv"), include_header=True
    )

    # 3) Large Price Frequency (>$300)
    print("Running large price frequency query...")
    price_300_df = query_large_price_frequency(
        session, url, SCENARIO_NAME, TARGET_REGIONS, 300, FIRST_YEAR
    )

    # Create the combined file with all regions
    price_300_pivot = price_300_df.pivot(index="fy", on="region", values="great_300")
    price_300_pivot.write_csv(
        os.path.join(OUTPUT_DIR, "300PricePrices.csv"), include_header=True
    )

    # 4) FCAS Prices (combined for all regions)
    print("Running FCAS prices query...")
    # Get FCAS data for all target regions combined
    all_fcas_data = []
    for region in TARGET_REGIONS:
        fcas_df = query_fcas_prices_per_region(
            session, url, SCENARIO_NAME, region, None, FIRST_YEAR
        )
        all_fcas_data.append(fcas_df)

    if all_fcas_data:
        # Import FCAS_SERVICES for column ordering
        from backend.dao.postgres.constants import FCAS_SERVICES

        # Combine all FCAS data
        combined_fcas_df = pl.concat(all_fcas_data)
        # Group by fiscal year and service, taking the average price across regions
        fcas_avg_df = (
            combined_fcas_df.group_by(["fy", "service"])
            .agg(pl.col("price").mean().alias("price"))
            .sort(["fy", "service"])
        )

        # Create pivot table
        fcas_pivot = fcas_avg_df.pivot(index="fy", on="service", values="price")

        # Reorder columns according to FCAS_SERVICES specification
        available_columns = ["fy"] + [
            col for col in FCAS_SERVICES if col in fcas_pivot.columns
        ]
        fcas_pivot = fcas_pivot.select(available_columns)

        fcas_pivot.write_csv(
            os.path.join(OUTPUT_DIR, "FCAS_Prices.csv"), include_header=True
        )

    # 5) NEM Capacity
    print("Running NEM capacity query...")
    nem_capacity_df = query_nem_capacity(session, url, SCENARIO_NAME, FILTERED_YEARS)
    nem_capacity_df.write_csv(
        os.path.join(OUTPUT_DIR, "NEM_Capacity.csv"), include_header=True
    )

    # 6) State & REZ Capacity
    print("Running state capacity queries...")
    for region in TARGET_REGIONS:
        # State capacity (without "State_" prefix)
        state_capacity_df = query_state_and_rez_capacity_buildout(
            session, url, SCENARIO_NAME, region=region, filtered_years=FILTERED_YEARS
        )
        state_capacity_df.write_csv(
            os.path.join(OUTPUT_DIR, f"{region}_Capacity.csv"), include_header=True
        )

        # REZ capacity for this region (only if REZs are explicitly selected)
        if region in REGION_REZS and REGION_REZS[region]:
            for rez in REGION_REZS[region]:
                rez_capacity_df = query_state_and_rez_capacity_buildout(
                    session,
                    url,
                    SCENARIO_NAME,
                    region=region,
                    rez=rez,
                    filtered_years=FILTERED_YEARS,
                )
                rez_capacity_df.write_csv(
                    os.path.join(OUTPUT_DIR, f"REZ_{region}_{rez}_Capacity.csv"),
                    include_header=True,
                )

    # 7) Battery Spreads (per duration) - only if battery durations are selected
    if BATTERY_DURATIONS:
        print("Running battery spreads queries...")
        for duration in BATTERY_DURATIONS:
            spreads_df = query_battery_spreads(
                session, url, SCENARIO_NAME, TARGET_REGIONS, duration, FIRST_YEAR
            )

            # Create the combined file with all regions
            spreads_pivot = spreads_df.pivot(index="fy", on="region", values="spread")
            spreads_pivot.write_csv(
                os.path.join(OUTPUT_DIR, f"Spreads_{duration}h.csv"),
                include_header=True,
            )
    else:
        print("Skipping battery spreads queries - no battery durations selected")

    # 8) NEM Generation
    print("Running NEM generation query...")
    nem_production_df = query_nem_production(
        session, url, SCENARIO_NAME, FILTERED_YEARS
    )

    # Create the combined file with all regions
    nem_production_df.write_csv(
        os.path.join(OUTPUT_DIR, "NEM_Generation.csv"), include_header=True
    )

    # 9) Regional Generation (if needed separately)
    print("Running regional generation queries...")
    for region in TARGET_REGIONS:
        state_production_df = query_state_production(
            session, url, SCENARIO_NAME, region, FILTERED_YEARS
        )
        state_production_df.write_csv(
            os.path.join(OUTPUT_DIR, f"{region}_Generation.csv"), include_header=True
        )

    print(f"✓ Saved CSV files to folder: {OUTPUT_DIR}")

    # Convert capacity and generation files to Excel with colors
    print("Converting capacity and generation files to Excel format with colors...")
    try:
        from backend.dao.postgres.excel_formatter import \
            convert_capacity_and_generation_files

        excel_files = convert_capacity_and_generation_files(OUTPUT_DIR)
        if excel_files:
            print(f"✓ Created {len(excel_files)} Excel files with color formatting")

            # Delete corresponding CSV files after successful Excel conversion
            deleted_csv_count = 0
            for excel_path in excel_files:
                # Convert Excel path to corresponding CSV path
                csv_path = excel_path.replace(".xlsx", ".csv")
                if os.path.exists(csv_path):
                    try:
                        os.remove(csv_path)
                        deleted_csv_count += 1
                    except Exception as e:
                        print(
                            f"Warning: Could not delete {os.path.basename(csv_path)}: {str(e)}"
                        )

            if deleted_csv_count > 0:
                print(
                    f"✓ Deleted {deleted_csv_count} CSV files (keeping only Excel versions)"
                )
        else:
            print("No capacity or generation files found to convert")
    except ImportError:
        print("Warning: openpyxl not available - skipping Excel formatting")
    except Exception as e:
        print(f"Warning: Error creating Excel files: {str(e)}")


def main():
    """Main entry point for the application."""
    run_with_config()


if __name__ == "__main__":
    # Run with default configuration or loaded from file
    main()
