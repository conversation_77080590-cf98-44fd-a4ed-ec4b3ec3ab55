"""
Acceptance tests for the Deck Builder application.

These tests validate that the application produces the same outputs as expected
by running the full application with predefined configurations and comparing
the generated CSV files against stored expected results.
"""

import json
import os
import shutil
import tempfile
from pathlib import Path
from typing import Dict, List

import numpy as np
import polars as pl
import pytest

# Test configuration
TEST_CONFIG_FILE = Path(__file__).parent / "test_config.json"
EXPECTED_DIR = Path(__file__).parent / "expected"
CONFIG_FILE_NAME = "config.json"


def load_test_config() -> Dict:
    """Load test configuration from JSON file."""
    with open(TEST_CONFIG_FILE, 'r') as f:
        return json.load(f)


def compare_csv_files(actual_file: Path, expected_file: Path, tolerance: float = 1e-6) -> bool:
    """
    Compare two CSV files for equality with numerical tolerance.
    
    Args:
        actual_file: Path to the actual output file
        expected_file: Path to the expected output file
        tolerance: Numerical tolerance for floating point comparisons
        
    Returns:
        True if files are equivalent, False otherwise
    """
    if not actual_file.exists():
        print(f"Actual file does not exist: {actual_file}")
        return False
        
    if not expected_file.exists():
        print(f"Expected file does not exist: {expected_file}")
        return False
    
    try:
        # Read both CSV files
        actual_df = pl.read_csv(actual_file)
        expected_df = pl.read_csv(expected_file)

        # Check if shapes match
        if actual_df.shape != expected_df.shape:
            print(f"Shape mismatch for {actual_file.name}: actual {actual_df.shape} vs expected {expected_df.shape}")
            return False

        # Check if columns match
        if actual_df.columns != expected_df.columns:
            print(f"Column mismatch for {actual_file.name}: actual {actual_df.columns} vs expected {expected_df.columns}")
            return False

        # Compare data with tolerance for numerical columns
        for col in actual_df.columns:
            actual_series = actual_df[col]
            expected_series = expected_df[col]

            # Check if column is numeric
            if actual_series.dtype in [pl.Float64, pl.Float32, pl.Int64, pl.Int32]:
                # Numerical comparison with tolerance
                try:
                    # Convert to numpy for comparison with tolerance
                    actual_values = actual_series.to_numpy()
                    expected_values = expected_series.to_numpy()

                    # Check if arrays are close within tolerance
                    if not np.allclose(actual_values, expected_values, rtol=tolerance, atol=tolerance, equal_nan=True):
                        print(f"Numerical data mismatch in column {col} for {actual_file.name}")
                        return False
                except Exception as e:
                    print(f"Error comparing numerical column {col} for {actual_file.name}: {str(e)}")
                    return False
            else:
                # Exact comparison for non-numerical columns
                if not actual_series.equals(expected_series):
                    print(f"Data mismatch in column {col} for {actual_file.name}")
                    return False

        return True
        
    except Exception as e:
        print(f"Error comparing {actual_file.name}: {str(e)}")
        return False


def run_application_with_config(config: Dict, output_dir: Path) -> bool:
    """
    Run the application with the given configuration.
    
    Args:
        config: Configuration dictionary
        output_dir: Directory where outputs will be generated
        
    Returns:
        True if application ran successfully, False otherwise
    """
    try:
        # Create config file
        config_file = Path(CONFIG_FILE_NAME)
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        # Import and run the main application
        import sys
        sys.path.insert(0, str(Path(__file__).parent.parent))
        
        from backend.main import main
        
        # Run the application
        main()
        
        # Clean up config file
        if config_file.exists():
            config_file.unlink()
            
        return True
        
    except Exception as e:
        print(f"Error running application: {str(e)}")
        return False


class TestAcceptance:
    """Acceptance tests for the Deck Builder application."""
    
    @pytest.fixture(autouse=True)
    def setup_test_environment(self):
        """Set up test environment before each test."""
        # Store original working directory
        self.original_cwd = os.getcwd()
        
        # Change to project root directory
        project_root = Path(__file__).parent.parent
        os.chdir(project_root)
        
        yield
        
        # Restore original working directory
        os.chdir(self.original_cwd)
    
    @pytest.mark.parametrize("test_name", load_test_config()["test_runs"].keys())
    def test_application_output(self, test_name: str):
        """
        Test that application produces expected outputs for a given configuration.
        
        Args:
            test_name: Name of the test configuration to run
        """
        test_config = load_test_config()
        test_run = test_config["test_runs"][test_name]
        
        config = test_run["config"]
        expected_files = test_run["expected_files"]
        
        # Create temporary directory for actual outputs
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Update config to use temporary output directory
            scenario_name = config["SCENARIO_NAME"]
            output_dir = temp_path / "outputs" / scenario_name.replace(" ", "_").replace("-", "_")
            
            # Run application
            success = run_application_with_config(config, output_dir)
            assert success, f"Application failed to run for test: {test_name}"
            
            # Check that output directory was created
            assert output_dir.exists(), f"Output directory not created: {output_dir}"
            
            # Compare each expected file
            expected_test_dir = EXPECTED_DIR / test_name
            mismatches = []
            
            for expected_file in expected_files:
                actual_file = output_dir / expected_file
                expected_file_path = expected_test_dir / expected_file
                
                if not compare_csv_files(actual_file, expected_file_path):
                    mismatches.append(expected_file)
            
            # Report results
            if mismatches:
                pytest.fail(f"Output files do not match expected results for test '{test_name}': {mismatches}")
    
    def test_generate_expected_outputs(self):
        """
        Helper test to generate expected outputs for the first time.
        This test should be run manually to populate the expected directory.
        """
        pytest.skip("This test is for generating expected outputs only")
        
        test_config = load_test_config()
        
        for test_name, test_run in test_config["test_runs"].items():
            config = test_run["config"]
            expected_files = test_run["expected_files"]
            
            # Run application
            success = run_application_with_config(config, None)
            assert success, f"Application failed to run for test: {test_name}"
            
            # Copy outputs to expected directory
            scenario_name = config["SCENARIO_NAME"]
            output_dir = Path("outputs") / scenario_name.replace(" ", "_").replace("-", "_")
            expected_test_dir = EXPECTED_DIR / test_name
            
            # Create expected directory
            expected_test_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy expected files
            for expected_file in expected_files:
                actual_file = output_dir / expected_file
                if actual_file.exists():
                    shutil.copy2(actual_file, expected_test_dir / expected_file)
                    print(f"Copied {expected_file} to expected directory")
                else:
                    print(f"Warning: Expected file {expected_file} not found in output")
