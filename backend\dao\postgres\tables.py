from sqlalchemy import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>loat, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


class Base(DeclarativeBase):
    pass


# Lookup Tables
class AbatementSolutions(Base):
    __tablename__ = "abatementsolutions"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    abatementsolution: Mapped[str] = mapped_column(String(256), nullable=True)
    abatementtype: Mapped[str] = mapped_column(String(256), nullable=True)


class AncillaryServices(Base):
    __tablename__ = "ancillaryservices"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    service: Mapped[str] = mapped_column(String(32), nullable=True)
    servicefullname: Mapped[str] = mapped_column(String(64), nullable=True)


class BalancingServices(Base):
    __tablename__ = "balancingservices"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    balancingservice: Mapped[str] = mapped_column(String(256), nullable=True)


class BalancingZones(Base):
    __tablename__ = "balancingzones"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    balancingzone: Mapped[str] = mapped_column(String(256), nullable=True)


class Commodities(Base):
    __tablename__ = "commodities"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    commodity: Mapped[str] = mapped_column(String(128), nullable=True)
    description: Mapped[str] = mapped_column(String(256), nullable=True)
    currency: Mapped[str] = mapped_column(String(8), nullable=True)


class Companies(Base):
    __tablename__ = "companies"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    company: Mapped[str] = mapped_column(String(64), nullable=True)
    clientcompany: Mapped[str] = mapped_column(String(64), nullable=True)
    plantid: Mapped[int] = mapped_column(Integer, nullable=True)
    shares: Mapped[float] = mapped_column(Float, nullable=True)
    startmmid: Mapped[int] = mapped_column(Integer, nullable=True)
    endmmid: Mapped[int] = mapped_column(Integer, nullable=True)


class ConversionFactors(Base):
    __tablename__ = "conversionfactors"
    conversionname: Mapped[str] = mapped_column(String(256), primary_key=True)
    inflationfactor: Mapped[float] = mapped_column(Float, nullable=True)
    username: Mapped[str] = mapped_column(String(127), nullable=True)
    defaultexchangerate: Mapped[float] = mapped_column(Float, nullable=True)


class Directions(Base):
    __tablename__ = "directions"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    direction: Mapped[str] = mapped_column(String(32), nullable=True)
    directionofpoweroutput: Mapped[str] = mapped_column(String(32), nullable=True)


class FuelTypes(Base):
    __tablename__ = "fueltypes"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    fueltype: Mapped[str] = mapped_column(String(32), nullable=True)
    fueltypefullname: Mapped[str] = mapped_column(String(64), nullable=True)


class HydrogenTransportMethods(Base):
    __tablename__ = "hydrogentransportmethods"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    hydrogentransportmethod: Mapped[str] = mapped_column(String(256), nullable=True)


class Industries(Base):
    __tablename__ = "industries"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    industry: Mapped[str] = mapped_column(String(256), nullable=True)


class Iterations(Base):
    __tablename__ = "iterations"
    iteration: Mapped[int] = mapped_column(Integer, primary_key=True)


class Regions(Base):
    __tablename__ = "regions"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    region: Mapped[str] = mapped_column(String(64), nullable=True)
    regionfullname: Mapped[str] = mapped_column(String(128), nullable=True)
    currency: Mapped[str] = mapped_column(String(8), nullable=True)


class Scenarios(Base):
    __tablename__ = "scenarios"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    hash: Mapped[str] = mapped_column(String(256), nullable=True)
    scenario: Mapped[str] = mapped_column(String(255), nullable=True)
    scenariotype: Mapped[str] = mapped_column(String(256), nullable=True)
    runtime: Mapped[str] = mapped_column(TIMESTAMP, nullable=True)
    runby: Mapped[str] = mapped_column(String(128), nullable=True)
    deleted: Mapped[bool] = mapped_column(Boolean, nullable=True)
    product: Mapped[str] = mapped_column(String(256), nullable=True)
    project: Mapped[str] = mapped_column(String(256), nullable=True)
    description: Mapped[str] = mapped_column(String(65535), nullable=True)
    published: Mapped[bool] = mapped_column(Boolean, nullable=True)
    iteration: Mapped[int] = mapped_column(Integer, nullable=True)
    totaliterations: Mapped[int] = mapped_column(Integer, nullable=True)
    retentionpolicy: Mapped[str] = mapped_column(String(32), nullable=True)


class ScenarioSettings(Base):
    __tablename__ = "scenariosettings"
    scenarioid: Mapped[int] = mapped_column(Integer, ForeignKey("scenarios.id"), primary_key=True)
    setting: Mapped[str] = mapped_column(String(256), primary_key=True)
    value: Mapped[str] = mapped_column(String(256), nullable=True)


class Technologies(Base):
    __tablename__ = "technologies"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    technologyfullname: Mapped[str] = mapped_column(String(64), nullable=True)
    technology: Mapped[str] = mapped_column(String(64), nullable=True)
    class_: Mapped[str] = mapped_column("class", String(64), nullable=True)  # 'class' is a Python keyword
    group: Mapped[str] = mapped_column(String(64), nullable=True)
    subgroup: Mapped[str] = mapped_column(String(64), nullable=True)
    exclude: Mapped[bool] = mapped_column(Boolean, nullable=True)
    excludefromcaptureprice: Mapped[bool] = mapped_column(Boolean, nullable=True)


class Time(Base):
    __tablename__ = "time"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    hourssince1990: Mapped[int] = mapped_column(Integer, nullable=True)
    halfhour: Mapped[int] = mapped_column(Integer, nullable=True)
    dayssince1990: Mapped[int] = mapped_column(Integer, nullable=True)
    weekday: Mapped[int] = mapped_column(Integer, nullable=True)
    weekdaytext: Mapped[str] = mapped_column(String(10), nullable=True)
    modelweekday: Mapped[int] = mapped_column(Integer, nullable=True)
    modelweekdaytext: Mapped[str] = mapped_column(String(10), nullable=True)
    hourinyear: Mapped[int] = mapped_column(Integer, nullable=True)
    date: Mapped[str] = mapped_column(TIMESTAMP, nullable=True)
    month: Mapped[int] = mapped_column(Integer, nullable=True)
    monthtext: Mapped[str] = mapped_column(String(10), nullable=True)
    quarter: Mapped[str] = mapped_column(String(8), nullable=True)
    halfyear: Mapped[str] = mapped_column(String(8), nullable=True)
    season: Mapped[str] = mapped_column(String(8), nullable=True)
    year: Mapped[int] = mapped_column(Integer, nullable=True)
    fiveyear: Mapped[int] = mapped_column(Integer, nullable=True)
    decade: Mapped[int] = mapped_column(Integer, nullable=True)
    dayofmonth: Mapped[int] = mapped_column(Integer, nullable=True)
    hourofday: Mapped[int] = mapped_column(Integer, nullable=True)
    endofhalfhourlyperiod: Mapped[str] = mapped_column(TIMESTAMP, nullable=True)
    modeldstoffset: Mapped[int] = mapped_column(Integer, nullable=True)


# Plant and Node related tables
class Nodes(Base):
    __tablename__ = "nodes"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    node: Mapped[str] = mapped_column(String(64), nullable=True)
    regionid: Mapped[int] = mapped_column(Integer, ForeignKey("regions.id"), nullable=True)


class LineGroups(Base):
    __tablename__ = "linegroups"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    linegroup: Mapped[str] = mapped_column(String(256), nullable=True)


class Lines(Base):
    __tablename__ = "lines"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    line: Mapped[str] = mapped_column(String(256), nullable=True)
    fromregionid: Mapped[int] = mapped_column(Integer, ForeignKey("regions.id"), nullable=True)
    toregionid: Mapped[int] = mapped_column(Integer, ForeignKey("regions.id"), nullable=True)
    linegroupid: Mapped[int] = mapped_column(Integer, ForeignKey("linegroups.id"), nullable=True)


class Plants(Base):
    __tablename__ = "plants"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    plant: Mapped[str] = mapped_column(String(64), nullable=True)
    regionid: Mapped[int] = mapped_column(Integer, ForeignKey("regions.id"), nullable=True)
    technologyid: Mapped[int] = mapped_column(Integer, ForeignKey("technologies.id"), nullable=True)
    fueltypeid: Mapped[int] = mapped_column(Integer, ForeignKey("fueltypes.id"), nullable=True)
    bmname: Mapped[str] = mapped_column(String(64), nullable=True)
    plantcorrelationid: Mapped[int] = mapped_column(Integer, nullable=True)
    startmmid: Mapped[int] = mapped_column(Integer, nullable=True)
    endmmid: Mapped[int] = mapped_column(Integer, nullable=True)
    rocband: Mapped[str] = mapped_column(String(32), nullable=True)
    efficiency: Mapped[float] = mapped_column(Float, nullable=True)
    type: Mapped[str] = mapped_column(String(32), nullable=True)
    isproductionnegative: Mapped[bool] = mapped_column(Boolean, nullable=True)
    nodeid: Mapped[int] = mapped_column(Integer, ForeignKey("nodes.id"), nullable=True)


# Main operational tables
class HalfHourlyRegion(Base):
    __tablename__ = "halfhourlyregion"

    scenarioid: Mapped[int] = mapped_column(Integer, ForeignKey("scenarios.id"))
    regionid: Mapped[int] = mapped_column(Integer, ForeignKey("regions.id"))
    timeid: Mapped[int] = mapped_column(Integer, ForeignKey("time.id"))
    wholesaleprice: Mapped[float] = mapped_column(Float, nullable=True)
    balancingmarketprice: Mapped[float] = mapped_column(Float, nullable=True)
    basedemand: Mapped[float] = mapped_column(Float, nullable=True)
    evdemand: Mapped[float] = mapped_column(Float, nullable=True)
    totaldemand: Mapped[float] = mapped_column(Float, nullable=True)
    energyexportedinmwh: Mapped[float] = mapped_column(Float, nullable=True)
    energyimportedinmwh: Mapped[float] = mapped_column(Float, nullable=True)
    co2intensityingramsperkwh: Mapped[float] = mapped_column(Float, nullable=True)
    curtailmentinmwh: Mapped[float] = mapped_column(Float, nullable=True)
    generationinmwh: Mapped[float] = mapped_column(Float, nullable=True)
    netexportinmwh: Mapped[float] = mapped_column(Float, nullable=True)
    netimportinmwh: Mapped[float] = mapped_column(Float, nullable=True)
    renewablegenerationinmwh: Mapped[float] = mapped_column(Float, nullable=True)
    thermalgenerationinmwh: Mapped[float] = mapped_column(Float, nullable=True)
    variablegenerationinmwh: Mapped[float] = mapped_column(Float, nullable=True)

    __mapper_args__ = {"primary_key": [scenarioid, regionid, timeid]}


class YearlyRegion(Base):
    __tablename__ = "yearlyregion"

    scenarioid: Mapped[int] = mapped_column(Integer, ForeignKey("scenarios.id"))
    regionid: Mapped[int] = mapped_column(Integer, ForeignKey("regions.id"))
    timeid: Mapped[int] = mapped_column(Integer, ForeignKey("time.id"))
    baseloadprice: Mapped[float] = mapped_column(Float, nullable=True)
    averagedailyspreadprice1h: Mapped[float] = mapped_column(Float, nullable=True)
    averagedailyspreadprice2h: Mapped[float] = mapped_column(Float, nullable=True)
    averagedailyspreadprice4h: Mapped[float] = mapped_column(Float, nullable=True)
    averagedailyspreadprice6h: Mapped[float] = mapped_column(Float, nullable=True)
    averagedailyspreadprice8h: Mapped[float] = mapped_column(Float, nullable=True)
    exchangerate: Mapped[float] = mapped_column(Float, nullable=True)
    averagedemand: Mapped[float] = mapped_column(Float, nullable=True)
    averagegeneration: Mapped[float] = mapped_column(Float, nullable=True)
    averageprice: Mapped[float] = mapped_column(Float, nullable=True)
    demandintwhpa: Mapped[float] = mapped_column(Float, nullable=True)
    generationintwhpa: Mapped[float] = mapped_column(Float, nullable=True)
    maxdemand: Mapped[float] = mapped_column(Float, nullable=True)
    maxprice: Mapped[float] = mapped_column(Float, nullable=True)
    mindemand: Mapped[float] = mapped_column(Float, nullable=True)
    minprice: Mapped[float] = mapped_column(Float, nullable=True)
    netexportintwhpa: Mapped[float] = mapped_column(Float, nullable=True)
    netimportintwhpa: Mapped[float] = mapped_column(Float, nullable=True)
    renewablegenerationintwhpa: Mapped[float] = mapped_column(Float, nullable=True)
    renewablegenerationshare: Mapped[float] = mapped_column(Float, nullable=True)
    thermalgenerationintwhpa: Mapped[float] = mapped_column(Float, nullable=True)
    variablegenerationintwhpa: Mapped[float] = mapped_column(Float, nullable=True)

    __mapper_args__ = {"primary_key": [scenarioid, regionid, timeid]}


class HalfHourlyRegionAncillary(Base):
    __tablename__ = "halfhourlyregionancillary"
    scenarioid: Mapped[int] = mapped_column(Integer, ForeignKey("scenarios.id"))
    regionid: Mapped[int] = mapped_column(Integer, ForeignKey("regions.id"))
    timeid: Mapped[int] = mapped_column(Integer, ForeignKey("time.id"))
    serviceid: Mapped[int] = mapped_column(Integer, ForeignKey("ancillaryservices.id"))
    clearingpricepermwperh: Mapped[float] = mapped_column(Float, nullable=True)
    demandmw: Mapped[float] = mapped_column(Float, nullable=True)
    supplymw: Mapped[float] = mapped_column(Float, nullable=True)

    __mapper_args__ = {"primary_key": [scenarioid, regionid, timeid, serviceid]}


class YearlyPlantOperations(Base):
    __tablename__ = "yearlyplantoperations"
    scenarioid: Mapped[int] = mapped_column(Integer, ForeignKey("scenarios.id"))
    plantid: Mapped[int] = mapped_column(Integer, ForeignKey("plants.id"))
    timeid: Mapped[int] = mapped_column(Integer, ForeignKey("time.id"))
    # Key operational metrics
    ageinyears: Mapped[float] = mapped_column(Float, nullable=True)
    availability: Mapped[float] = mapped_column(Float, nullable=True)
    averageproductioninmw: Mapped[float] = mapped_column(Float, nullable=True)
    capacity: Mapped[float] = mapped_column(Float, nullable=True)  # Used by capacity queries
    capacityfactor: Mapped[float] = mapped_column(Float, nullable=True)
    capacityinmw: Mapped[float] = mapped_column(Float, nullable=True)
    canproductionintwh: Mapped[float] = mapped_column(Float, nullable=True)
    productionintwh: Mapped[float] = mapped_column(Float, nullable=True)
    curtailmentintwh: Mapped[float] = mapped_column(Float, nullable=True)
    fuelconsumptionintwh: Mapped[float] = mapped_column(Float, nullable=True)
    grossproductionintwh: Mapped[float] = mapped_column(Float, nullable=True)  # Used by generation queries
    loadfactor: Mapped[float] = mapped_column(Float, nullable=True)
    maxproductioninmw: Mapped[float] = mapped_column(Float, nullable=True)
    minproductioninmw: Mapped[float] = mapped_column(Float, nullable=True)
    netproductionintwh: Mapped[float] = mapped_column(Float, nullable=True)  # Used by generation queries
    startupcount: Mapped[float] = mapped_column(Float, nullable=True)
    shutdowncount: Mapped[float] = mapped_column(Float, nullable=True)
    # Storage specific
    pumpedstoragepumpingintwh: Mapped[float] = mapped_column(Float, nullable=True)
    storagechargeintwh: Mapped[float] = mapped_column(Float, nullable=True)
    storagedischargeintwh: Mapped[float] = mapped_column(Float, nullable=True)
    storageefficiency: Mapped[float] = mapped_column(Float, nullable=True)

    __mapper_args__ = {"primary_key": [scenarioid, plantid, timeid]}


class YearlyRegionTechnologyOperations(Base):
    __tablename__ = "yearlyregiontechnologyoperations"
    scenarioid: Mapped[int] = mapped_column(Integer, ForeignKey("scenarios.id"))
    regionid: Mapped[int] = mapped_column(Integer, ForeignKey("regions.id"))
    timeid: Mapped[int] = mapped_column(Integer, ForeignKey("time.id"))
    technologyid: Mapped[int] = mapped_column(Integer, ForeignKey("technologies.id"))
    canproductionintwh: Mapped[float] = mapped_column(Float, nullable=True)
    capacityfactor: Mapped[float] = mapped_column(Float, nullable=True)
    capacityinmw: Mapped[float] = mapped_column(Float, nullable=True)
    curtailmentintwh: Mapped[float] = mapped_column(Float, nullable=True)
    grossproductionintwh: Mapped[float] = mapped_column(Float, nullable=True)
    netproductionintwh: Mapped[float] = mapped_column(Float, nullable=True)

    __mapper_args__ = {"primary_key": [scenarioid, regionid, timeid, technologyid]}


# Settings tables
class PlantSettings(Base):
    __tablename__ = "plantsettings"
    scenarioid: Mapped[int] = mapped_column(Integer, ForeignKey("scenarios.id"))
    plantid: Mapped[int] = mapped_column(Integer, ForeignKey("plants.id"))
    isnewbuild: Mapped[bool] = mapped_column(Boolean, nullable=True)
    isendogenous: Mapped[bool] = mapped_column(Boolean, nullable=True)
    subregion: Mapped[str] = mapped_column(String(64), nullable=True)
    discountrate: Mapped[float] = mapped_column(Float, nullable=True)
    mothballcost: Mapped[float] = mapped_column(Float, nullable=True)
    balancingzoneid: Mapped[int] = mapped_column(Integer, nullable=True)
    constructionperiod: Mapped[float] = mapped_column(Float, nullable=True)
    group: Mapped[str] = mapped_column(String(64), nullable=True)
    exchangerate: Mapped[float] = mapped_column(Float, nullable=True)

    __mapper_args__ = {"primary_key": [scenarioid, plantid]}
