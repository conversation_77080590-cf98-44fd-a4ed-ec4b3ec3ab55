import logging
import os

from sqlalchemy import create_engine
from sqlalchemy import orm as sa_orm
from sqlalchemy.dialects import registry
from sqlalchemy.dialects.postgresql.psycopg2 import PGDialect_psycopg2
from sqlalchemy.engine.url import URL

# ——— Config & Inputs ———
currency_year = "2024"
currency_account = f"audcurrency{currency_year}_production"
username = currency_account
password = os.environ.get("DWH_AUS_2024")
host = os.environ.get("PGHOST", "dwh.auroraer.com")
port = int(os.environ.get("PGPORT", 5439))
database = "production"
sslmode = "require"

logger = logging.getLogger(__name__)


class RedshiftPostgresDialect(PGDialect_psycopg2):
    def _set_backslash_escapes(self, connection):
        self._backslash_escapes = "off"
        self._supports_statement_cache = True


registry.register("redshift_custom", __name__, "RedshiftPostgresDialect")


def connect():
    url = URL.create(
        drivername="redshift_custom",
        host=host,
        port=5439,
        database=database,
        username=currency_account,
        password=password,
    )

    logger.info("Connecting to PostgreSQL")
    engine = create_engine(url, connect_args={"sslmode": "require"})

    Session = sa_orm.sessionmaker()
    Session.configure(bind=engine)
    session = Session()

    return session, engine.url.render_as_string(hide_password=False)
