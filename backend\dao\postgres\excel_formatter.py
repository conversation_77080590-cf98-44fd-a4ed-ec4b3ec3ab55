"""
Excel formatting utilities for capacity and generation files.

This module provides functions to convert CSV files to Excel format
with appropriate color formatting based on the prototype design.
"""

import os
from pathlib import Path
from typing import List, Optional

import polars as pl
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows

from .excel_colors import apply_colors_to_worksheet


def csv_to_excel_with_colors(
    csv_file_path: str, excel_file_path: Optional[str] = None
) -> str:
    """
    Convert a CSV file to Excel format with color formatting.

    Args:
        csv_file_path: Path to the input CSV file
        excel_file_path: Path for the output Excel file (optional)

    Returns:
        Path to the created Excel file
    """
    if not os.path.exists(csv_file_path):
        raise FileNotFoundError(f"CSV file not found: {csv_file_path}")

    # Generate Excel file path if not provided
    if excel_file_path is None:
        csv_path = Path(csv_file_path)
        excel_file_path = str(csv_path.with_suffix(".xlsx"))

    # Read CSV file
    try:
        df_polars = pl.read_csv(csv_file_path)
        # Convert to pandas for openpyxl compatibility
        df = df_polars.to_pandas()
    except Exception as e:
        raise ValueError(f"Error reading CSV file {csv_file_path}: {str(e)}")

    # Create Excel workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "Data"

    # Write dataframe to worksheet
    for r in dataframe_to_rows(df, index=False, header=True):
        ws.append(r)

    # Apply color formatting
    apply_colors_to_worksheet(ws, has_header=True)

    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
        ws.column_dimensions[column_letter].width = adjusted_width

    # Save Excel file
    wb.save(excel_file_path)
    return excel_file_path


def convert_capacity_and_generation_files(output_dir: str) -> List[str]:
    """
    Convert all capacity and generation CSV files in a directory to Excel format.

    Args:
        output_dir: Directory containing the CSV files

    Returns:
        List of paths to created Excel files
    """
    if not os.path.exists(output_dir):
        raise FileNotFoundError(f"Output directory not found: {output_dir}")

    # Define patterns for capacity and generation files
    capacity_patterns = [
        "NEM_Capacity.csv",
        "*_Capacity.csv",  # Covers both regional and REZ capacity files
    ]

    generation_patterns = [
        "NEM_Generation.csv",  # Renamed from Total_Generation_Buildout
        "*_Generation.csv",  # Covers regional generation files (without State_ prefix)
    ]

    all_patterns = capacity_patterns + generation_patterns
    excel_files = []

    # Find and convert matching files
    output_path = Path(output_dir)
    for pattern in all_patterns:
        if "*" in pattern:
            # Handle wildcard patterns
            pattern_parts = pattern.split("*")
            if len(pattern_parts) == 2:
                prefix, suffix = pattern_parts
                for file_path in output_path.glob(f"{prefix}*{suffix}"):
                    if file_path.is_file():
                        try:
                            excel_path = csv_to_excel_with_colors(str(file_path))
                            excel_files.append(excel_path)
                            print(
                                f"Converted {file_path.name} to {Path(excel_path).name}"
                            )
                        except Exception as e:
                            print(f"Error converting {file_path.name}: {str(e)}")
        else:
            # Handle exact file names
            file_path = output_path / pattern
            if file_path.exists():
                try:
                    excel_path = csv_to_excel_with_colors(str(file_path))
                    excel_files.append(excel_path)
                    print(f"Converted {file_path.name} to {Path(excel_path).name}")
                except Exception as e:
                    print(f"Error converting {file_path.name}: {str(e)}")

    return excel_files


def format_single_file(csv_file_path: str) -> Optional[str]:
    """
    Format a single CSV file as Excel with colors.

    Args:
        csv_file_path: Path to the CSV file

    Returns:
        Path to the Excel file if successful, None otherwise
    """
    try:
        excel_path = csv_to_excel_with_colors(csv_file_path)
        print(
            f"Successfully formatted {Path(csv_file_path).name} as {Path(excel_path).name}"
        )
        return excel_path
    except Exception as e:
        print(f"Error formatting {Path(csv_file_path).name}: {str(e)}")
        return None


# Export main functions
__all__ = [
    "csv_to_excel_with_colors",
    "convert_capacity_and_generation_files",
    "format_single_file",
]
