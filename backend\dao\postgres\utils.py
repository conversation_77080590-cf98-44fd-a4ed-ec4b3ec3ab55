import polars as pl
from sqlalchemy import case

from backend.dao.postgres.constants import (FIRST_MONTH_FISCAL_YEAR,
                                            MAX_HIGH_PRICE, MIN_PRICE)
from backend.dao.postgres.tables import HalfHourlyRegion, Time


def polaris_dataframe_from_sqlalchemy(session, stmt, url):
    stmt = str(
        stmt.compile(
            dialect=session.bind.dialect,
            compile_kwargs={"literal_binds": True},
        )
    )
    res = pl.read_database_uri(stmt, url)
    return res


def fiscal_year_stmt():
    return case((Time.month >= FIRST_MONTH_FISCAL_YEAR, Time.year + 1), else_=Time.year)


def price_greater_than_ceiling():
    return case((HalfHourlyRegion.wholesaleprice >= MAX_HIGH_PRICE, 1))


def price_lower_than_floor_stmt():
    return case((HalfHourlyRegion.wholesaleprice <= MIN_PRICE, 1))
