"""
Excel color constants for capacity and generation files.

This module defines the color scheme to be applied to capacity and generation
CSV files to match the NEM_Capacity_prototype.xlsm formatting exactly.

Color matching uses EXACT matches only - no partial or case-insensitive matching.
Column names must match exactly as defined in the mappings.
"""

from openpyxl.styles import Font, PatternFill

# Color definitions from NEM_Capacity_prototype.xlsm (hex codes without FF prefix)
COLORS = {
    # Specific technology colors from prototype
    "lignite": "5E2411",  # Dark brown
    "coal": "441A0D",  # Darker brown
    "ccgt": "5A5A5A",  # Gray
    "solar": "A6B727",  # Olive green
    "rooftop_solar": "D0DE63",  # Light green
    "wind_onshore": "9DB1CF",  # Light blue
    "wind_offshore": "6F8DB9",  # Medium blue
    "hydro": "4C6C9C",  # Dark blue
    "pumped_storage": "4E5F6E",  # Dark gray-blue
    "gas_peaker": "A6964E",  # Olive brown
    "btm_battery": "FFF2BF",  # Very light yellow
    "battery_storage": "E9E261",  # Yellow
    # Default/other columns (white background from prototype)
    "default_bg": "FFFFFF",  # White background
    "fy_bg": "FFFFFF",  # White for FY column (matches prototype)
    "aggregates_bg": "FFFFFF",  # White for aggregate columns
    # Font colors
    "default_font": "000000",  # Black font
}

# Technology to color mapping (exact matches from prototype)
TECHNOLOGY_COLORS = {
    # Exact column names from prototype with their specific colors
    "Lignite": "lignite",
    "Coal": "coal",
    "CCGT": "ccgt",
    "Solar": "solar",
    "Rooftop solar": "rooftop_solar",
    "Wind onshore": "wind_onshore",
    "Wind offshore": "wind_offshore",
    "Hydro": "hydro",
    "Pumped Storage": "pumped_storage",
    "Gas / Oil Peaker": "gas_peaker",
    "BTM Battery Storage": "btm_battery",
    "Battery Storage": "battery_storage",
    # Additional mappings for variations
    "Rooftop Solar": "rooftop_solar",  # Capital S variant
    "Peaking": "gas_peaker",  # Alternative name
    "Hydrogen Peaker": "gas_peaker",  # Similar technology
    "Nuclear": "lignite",  # Use lignite color for nuclear
    "BTM Battery Discharge": "btm_battery",
    "EVs": "battery_storage",
    # Aggregate columns (white background like in prototype)
    "baseload": "aggregates_bg",
    "zero_carbon": "aggregates_bg",
    "flexible": "aggregates_bg",
    "dispatchable": "aggregates_bg",
    "Other": "aggregates_bg",
}

# Column-specific colors for common aggregate columns (white background like prototype)
# Using exact case-sensitive matches only
AGGREGATE_COLUMN_COLORS = {
    "fy": "fy_bg",  # White background for FY
    "baseload": "aggregates_bg",  # White background
    "zero_carbon": "aggregates_bg",  # White background
    "flexible": "aggregates_bg",  # White background
    "dispatchable": "aggregates_bg",  # White background
    "Other": "aggregates_bg",  # White background for Other (exact case)
    "total_capacity": "aggregates_bg",
    "total_generation": "aggregates_bg",
    "generation": "aggregates_bg",  # For generation column
    "load": "aggregates_bg",  # For load column
    "technology": "aggregates_bg",  # For technology column
}


def get_color_for_column(column_name: str) -> str:
    """
    Get the appropriate color for a given column name based on prototype.
    Uses exact matches only - no partial or case-insensitive matching.

    Args:
        column_name: Name of the column

    Returns:
        Color key from COLORS dictionary
    """
    # Check exact technology matches (case sensitive to match prototype exactly)
    if column_name in TECHNOLOGY_COLORS:
        return TECHNOLOGY_COLORS[column_name]

    # Check exact aggregate column matches (case sensitive)
    if column_name in AGGREGATE_COLUMN_COLORS:
        return AGGREGATE_COLUMN_COLORS[column_name]

    # Default to white background (like prototype)
    return "default_bg"


def create_pattern_fill(color_key: str) -> PatternFill:
    """
    Create a PatternFill object for the given color key.

    Args:
        color_key: Key from COLORS dictionary

    Returns:
        PatternFill object
    """
    if color_key in COLORS:
        color_hex = COLORS[color_key]
        # Ensure color is 6 characters and add FF prefix for full opacity
        if len(color_hex) == 6:
            color_hex = "FF" + color_hex
        elif len(color_hex) == 8 and not color_hex.startswith("FF"):
            # If it has 8 chars but doesn't start with FF, assume it needs FF prefix
            if color_hex.startswith("00"):
                color_hex = "FF" + color_hex[2:]
            else:
                color_hex = "FF" + color_hex

        return PatternFill(
            start_color=color_hex, end_color=color_hex, fill_type="solid"
        )
    else:
        default_color = "FF" + COLORS["default_bg"]
        return PatternFill(
            start_color=default_color, end_color=default_color, fill_type="solid"
        )


def create_font(color_key: str = "default_font", bold: bool = False) -> Font:
    """
    Create a Font object for the given color key.

    Args:
        color_key: Key from COLORS dictionary for font color
        bold: Whether the font should be bold

    Returns:
        Font object
    """
    font_color = COLORS.get(
        f"{color_key.replace('_bg', '')}_font", COLORS["default_font"]
    )
    return Font(color=font_color, bold=bold)


def apply_colors_to_worksheet(worksheet, has_header: bool = True):
    """
    Apply color formatting to an Excel worksheet based on column names.

    Args:
        worksheet: openpyxl worksheet object
        has_header: Whether the first row contains headers
    """
    if not worksheet.max_row or not worksheet.max_column:
        return

    # Get column names from header row
    if has_header and worksheet.max_row > 0:
        header_row = 1
        column_names = []
        for col in range(1, worksheet.max_column + 1):
            cell_value = worksheet.cell(row=header_row, column=col).value
            column_names.append(str(cell_value) if cell_value else f"Column_{col}")

        # Apply header formatting
        for col in range(1, worksheet.max_column + 1):
            cell = worksheet.cell(row=header_row, column=col)
            cell.fill = create_pattern_fill("header_bg")
            cell.font = create_font("header_font", bold=True)

        # Apply data formatting
        for col, column_name in enumerate(column_names, 1):
            color_key = get_color_for_column(column_name)
            fill = create_pattern_fill(color_key)
            font = create_font(color_key.replace("_bg", "_font"))

            # Apply to all data rows (skip header)
            for row in range(header_row + 1, worksheet.max_row + 1):
                cell = worksheet.cell(row=row, column=col)
                cell.fill = fill
                cell.font = font
    else:
        # No header, apply default formatting
        for row in range(1, worksheet.max_row + 1):
            for col in range(1, worksheet.max_column + 1):
                cell = worksheet.cell(row=row, column=col)
                cell.fill = create_pattern_fill("default_bg")
                cell.font = create_font("default_font")


# Export the main functions and constants
__all__ = [
    "COLORS",
    "TECHNOLOGY_COLORS",
    "AGGREGATE_COLUMN_COLORS",
    "get_color_for_column",
    "create_pattern_fill",
    "create_font",
    "apply_colors_to_worksheet",
]
