import os
import pandas as pd
import psycopg2
import warnings

warnings.simplefilter(action='ignore', category=UserWarning)

# ——— Config & Inputs ———
currency_year    = "2024"
currency_account = f"audcurrency{currency_year}_production"
username         = currency_account
password         = os.environ.get("DWH_AUS_2024")
host             = os.environ.get("PGHOST", "dwh.auroraer.com")
port             = int(os.environ.get("PGPORT", 5439))
database         = "production"
sslmode          = "require"

# Interactive inputs
target_scenarios  = [s.strip() for s in input("Warehouse IDs (comma-separated): ").split(",")]
target_regions    = [r.strip() for r in input("Regions(comma-separated): ").split(",")]
global_region_rezs = {}
for region in target_regions:
    rezs = input(f"REZs for {region} (comma-separated, blank ok): ")
    global_region_rezs[region] = [z.strip() for z in rezs.split(",") if z.strip()]
battery_durations = [d.strip() for d in input("Durations (hours)(comma-separated): ").split(",") if d.strip()]

# Technology → tech_group mapping
technology_mapping = {
    'baa':     ['Battery Storage'],
    'bat_btm': ['BTM Battery Storage'],
    'ccg':     ['CCGT'],
    'nsg':     ['CCGT'],
    'coa':     ['Coal'],
    'com':     ['Coal'],
    'lig':     ['Lignite'],
    'lol_ocg': ['Gas / Oil Peaker'],
    'lol_rge': ['Gas / Oil Peaker'],
    'h_ocgt':  ['Gas / Oil Peaker'],
    'ico':     ['Gas / Oil Peaker'],
    'ocg':     ['Gas / Oil Peaker'],
    'rge':     ['Gas / Oil Peaker'],
    'vpp':     ['BTM Battery Storage'],
    'pum':     ['Pumped Storage'],
    'sol':     ['Solar'],
    'sol_csp': ['Solar'],
    'sol_btm': ['Rooftop solar'],
    'sto':     ['Hydro'],
    'ror':     ['Hydro'],
    'won':     ['Wind onshore'],
    'wof':     ['Wind offshore']
}

# Column order for capacity sheets
column_order = [
    'fy', 'Lignite', 'Coal', 'CCGT',
    'Solar', 'Rooftop solar', 'Wind onshore', 'Wind offshore',
    'Hydro', 'Pumped Storage', 'Gas / Oil Peaker',
    'BTM Battery Storage', 'Battery Storage'
]

# Aggregates for capacity calculations
aggregates = {
    "baseload":    ["Lignite", "Coal", "CCGT"],
    "zero_carbon": ["Solar", "Rooftop solar", "Wind onshore", "Wind offshore","Hydro"],
    "flexible":    ["Pumped Storage", "Gas / Oil Peaker", "BTM Battery Storage", "Battery Storage"]
}

# Connect to the data warehouse
conn = psycopg2.connect(
    host=host, port=port, user=username,
    password=password, database=database, sslmode=sslmode
)
conn.set_session(autocommit=True)

for hash_id in target_scenarios:
    # Lookup scenario by hash
    df_scen = pd.read_sql("SELECT id, hash, scenario FROM scenarios;", conn)
    row     = df_scen[df_scen["hash"] == hash_id]
    if row.empty:
        print(f"[SKIP] scenario hash {hash_id} not found.")
        continue

    scen_name = row["scenario"].iloc[0]
    print(f"\n=== Processing scenario: {scen_name} ===")
    out_dir = f"{scen_name}"
    os.makedirs(out_dir, exist_ok=True)

    # 1) TWA Yearly Prices
    print(" • Running TWA SQL…")
    twa_sql = f"""
        SELECT
          (CASE WHEN t.month >= 7 THEN t.year+1 ELSE t.year END) AS fy,
          yr.baseloadprice,
          r.region
        FROM yearlyregion yr
        JOIN time t        ON yr.timeid = t.id
        JOIN regions r     ON yr.regionid = r.id
        JOIN scenarios s   ON yr.scenarioid = s.id
        WHERE s.scenario = '{scen_name}'
          AND t.date >= '2025-07-01'
          AND r.region IN ({', '.join(f"'{r}'" for r in target_regions)})
        GROUP BY fy, r.region, yr.baseloadprice
        ORDER BY fy;
    """
    twa_df    = pd.read_sql(twa_sql, conn)
    twa_pivot = twa_df.pivot_table(index="fy", columns="region", values="baseloadprice").reset_index()
    print(" • Writing CSV: TWA")
    twa_pivot.to_csv(os.path.join(out_dir, "TWA.csv"), index=False)

    # 2) Negative-Price Frequency (<0)
    print(" • Running negative-price SQL…")
    neg_sql = f"""
        SELECT
          (CASE WHEN t.month >= 7 THEN t.year+1 ELSE t.year END) AS fy,
          r.region,
          COUNT(CASE WHEN hhr.wholesaleprice < 0 THEN 1 END) AS negative_count,
          COUNT(*) AS total_count
        FROM halfhourlyregion hhr
        JOIN time t        ON hhr.timeid = t.id
        JOIN regions r     ON hhr.regionid = r.id
        JOIN scenarios s   ON hhr.scenarioid = s.id
        WHERE s.scenario = '{scen_name}'
          AND r.region IN ({', '.join(f"'{r}'" for r in target_regions)})
        GROUP BY fy, r.region
        ORDER BY fy, r.region;
    """
    neg_df         = pd.read_sql(neg_sql, conn)
    neg_df["pct%"] = 100 * neg_df["negative_count"] / neg_df["total_count"]
    neg_pivot      = neg_df.pivot_table(index="fy", columns="region", values="pct%").reset_index()
    print(" • Writing CSV: NegativePrices")
    neg_pivot.to_csv(os.path.join(out_dir, "NegativePrices.csv"), index=False)

    # 3) >300 Price Frequency
    print(" • Running >300 price frequency SQL…")
    price_300_sql = f"""
        SELECT
          (CASE WHEN t.month >= 7 THEN t.year+1 ELSE t.year END) AS fy,
          r.region,
          COUNT(CASE WHEN hhr.wholesaleprice > 300 THEN 1 END) AS great_300,
          COUNT(*) AS total_count
        FROM halfhourlyregion hhr
        JOIN time t        ON hhr.timeid = t.id
        JOIN regions r     ON hhr.regionid = r.id
        JOIN scenarios s   ON hhr.scenarioid = s.id
        WHERE s.scenario = '{scen_name}'
          AND r.region IN ({', '.join(f"'{r}'" for r in target_regions)})
        GROUP BY fy, r.region
        ORDER BY fy, r.region;
    """
    price_300_df = pd.read_sql(price_300_sql, conn)
    price_300_pivot = price_300_df.pivot_table(index="fy", columns="region", values="great_300").reset_index()
    print(" • Writing CSV: 300PricePrices")
    price_300_pivot.to_csv(os.path.join(out_dir, "300PricePrices.csv"), index=False)

    # 4) FCAS Prices (per region)
    services = [
        'raiseRegulation','lowerRegulation','raise6secContingency',
        'raiseFFR','raise60secContingency','raise5minContingency'
    ]
    for region in target_regions:
        print(f" • Running FCAS SQL for {region}…")
        fcas_sql = f"""
            SELECT
              (CASE WHEN c.month >= 7 THEN c.year+1 ELSE c.year END) AS fy,
              d.service,
              AVG(a.clearingpricepermwperh) AS price
            FROM halfhourlyregionancillary a
            JOIN time c               ON a.timeid    = c.id
            JOIN ancillaryservices d ON a.serviceid = d.id
            JOIN regions r            ON a.regionid  = r.id
            JOIN scenarios s          ON a.scenarioid = s.id
            WHERE s.scenario = '{scen_name}'
              AND r.region = '{region}'
              AND d.service IN ({', '.join(f"'{svc}'" for svc in services)})
            GROUP BY fy, d.service
            ORDER BY fy;
        """
        fcas_df = pd.read_sql(fcas_sql, conn).pivot_table(
            index="fy", columns="service", values="price"
        ).reset_index()
        sheet = f"FCAS_{region}"
        print(f" • Writing CSV: {sheet}")
        fcas_df.to_csv(os.path.join(out_dir, f"{sheet}.csv"), index=False)

    # 5) NEM Capacity
    print(" • Running NEM capacity SQL…")
    nem_sql = f"""
        SELECT
          (CASE WHEN t.month > 6 THEN t.year+1 ELSE t.year END) AS fy,
          tech.technology,
          SUM(ypo.capacity) AS capacity
        FROM yearlyplantoperations ypo
        JOIN time t         ON ypo.timeid    = t.id
        JOIN plants p       ON ypo.plantid   = p.id
        JOIN scenarios s    ON ypo.scenarioid= s.id
        JOIN technologies tech ON p.technologyid = tech.id
        JOIN regions r      ON p.regionid     = r.id
        WHERE s.scenario = '{scen_name}'
          AND tech.technology NOT IN ('evs','evx')
        GROUP BY fy, tech.technology
        ORDER BY fy, tech.technology;
    """
    nem_raw = pd.read_sql(nem_sql, conn)
    expanded = []
    for _, row in nem_raw.iterrows():
        for grp in technology_mapping.get(row["technology"], ["Other"]):
            new = row.copy()
            new["tech_group"] = grp
            expanded.append(new)
    nem_expanded = pd.DataFrame(expanded)
    nem_cap      = nem_expanded.pivot_table(
        index="fy", columns="tech_group", values="capacity", aggfunc="sum"
    ).reset_index()
    nem_cap = nem_cap.fillna(0)
    # Ensure all required columns exist before aggregation
    for col in column_order[1:]:  # Skip 'fy' column
        if col not in nem_cap.columns:
            nem_cap[col] = 0
    # zero-fill, aggregates, dispatchable
    for name, comps in aggregates.items():
        # Only sum columns that exist
        existing_comps = [c for c in comps if c in nem_cap.columns]
        nem_cap[name] = nem_cap[existing_comps].sum(axis=1) if existing_comps else 0
    nem_cap["dispatchable"] = nem_cap["baseload"] + nem_cap["flexible"]
    # enforce column order
    nem_cap = nem_cap[column_order + [c for c in nem_cap.columns if c not in column_order]]
    print(" • Writing CSV: NEM_Capacity")
    nem_cap.to_csv(os.path.join(out_dir, "NEM_Capacity.csv"), index=False)

    # 6) State & REZ Capacity
    for region in target_regions:
        # State capacity
        print(f" • Running STATE capacity SQL for {region}…")
        state_sql = f"""
            SELECT
              (CASE WHEN t.month > 6 THEN t.year+1 ELSE t.year END) AS fy,
              tech.technology,
              SUM(ypo.capacity) AS capacity
            FROM yearlyplantoperations ypo
            JOIN time t         ON ypo.timeid    = t.id
            JOIN plants p       ON ypo.plantid   = p.id
            JOIN scenarios s    ON ypo.scenarioid= s.id
            JOIN technologies tech ON p.technologyid = tech.id
            JOIN regions r      ON p.regionid     = r.id
            WHERE s.scenario = '{scen_name}'
              AND tech.technology NOT IN ('evs','evx')
              AND r.region = '{region}'
            GROUP BY fy, tech.technology
            ORDER BY fy, tech.technology;
        """
        st_raw = pd.read_sql(state_sql, conn)
        exp    = []
        for _, row in st_raw.iterrows():
            for grp in technology_mapping.get(row["technology"], ["Other"]):
                new = row.copy()
                new["tech_group"] = grp
                exp.append(new)
        st_df = pd.DataFrame(exp).pivot_table(
            index="fy", columns="tech_group", values="capacity", aggfunc="sum"
        ).reset_index()
        st_df = st_df.fillna(0)
        # Ensure all required columns exist before aggregation
        for col in column_order[1:]:  # Skip 'fy' column
            if col not in st_df.columns:
                st_df[col] = 0
        for name, comps in aggregates.items():
            # Only sum columns that exist
            existing_comps = [c for c in comps if c in st_df.columns]
            st_df[name] = st_df[existing_comps].sum(axis=1) if existing_comps else 0
        st_df["dispatchable"] = st_df["baseload"] + st_df["flexible"]
        st_df = st_df[column_order + [c for c in st_df.columns if c not in column_order]]
        sheet = f"State_{region}_Capacity"
        print(f" • Writing CSV: {sheet}")
        st_df.to_csv(os.path.join(out_dir, f"{sheet}.csv"), index=False)

        # REZ capacity for this region
        for rez in global_region_rezs.get(region, []):
            print(f" • Running REZ capacity SQL for {region}/{rez}…")
            rez_sql = f"""
                SELECT
                  (CASE WHEN t.month > 6 THEN t.year+1 ELSE t.year END) AS fy,
                  tec.technology,
                  SUM(ypo.capacity) AS capacity
                FROM yearlyplantoperations ypo
                JOIN time t           ON ypo.timeid      = t.id
                JOIN plants p         ON ypo.plantid     = p.id
                JOIN scenarios s      ON ypo.scenarioid  = s.id
                JOIN technologies tec ON p.technologyid  = tec.id
                JOIN regions r        ON p.regionid      = r.id
                JOIN plantsettings ps ON ps.plantid=p.id AND ps.scenarioid=s.id
                WHERE s.scenario = '{scen_name}'
                  AND tec.technology NOT IN ('evs','evx')
                  AND r.region = '{region}'
                  AND ps.subregion = '{rez}'
                GROUP BY fy, tec.technology
                ORDER BY fy, tec.technology;
            """
            rz_raw = pd.read_sql(rez_sql, conn)
            exp2   = []
            for _, row in rz_raw.iterrows():
                for grp in technology_mapping.get(row["technology"], ["Other"]):
                    new = row.copy()
                    new["tech_group"] = grp
                    exp2.append(new)
            rz_df = pd.DataFrame(exp2).pivot_table(
                index="fy", columns="tech_group", values="capacity", aggfunc="sum"
            ).reset_index()
            rz_df = rz_df.fillna(0)
            # Ensure all required columns exist before aggregation
            for col in column_order[1:]:  # Skip 'fy' column
                if col not in rz_df.columns:
                    rz_df[col] = 0
            for name, comps in aggregates.items():
                # Only sum columns that exist
                existing_comps = [c for c in comps if c in rz_df.columns]
                rz_df[name] = rz_df[existing_comps].sum(axis=1) if existing_comps else 0
            rz_df["dispatchable"] = rz_df["baseload"] + rz_df["flexible"]
            rz_df = rz_df[column_order + [c for c in rz_df.columns if c not in column_order]]
            sheet = f"REZ_{region}_{rez}_Capacity"
            print(f" • Writing CSV: {sheet}")
            rz_df.to_csv(os.path.join(out_dir, f"{sheet}.csv"), index=False)

    # 7) Spreads (per duration)
    for dur in battery_durations:
        print(f" • Running spreads SQL for {dur}h…")
        spread_sql = f"""
            SELECT
              (CASE WHEN t.month >= 7 THEN t.year+1 ELSE t.year END) AS fy,
              r.region,
              yr.averagedailyspreadprice{dur}h AS spread
            FROM yearlyregion yr
            JOIN time t      ON yr.timeid   = t.id
            JOIN regions r   ON yr.regionid = r.id
            JOIN scenarios s ON yr.scenarioid = s.id
            WHERE s.scenario = '{scen_name}'
              AND r.region IN ({', '.join(f"'{r}'" for r in target_regions)})
            GROUP BY fy, r.region, yr.averagedailyspreadprice{dur}h
            ORDER BY fy, r.region;
        """
        sp_df    = pd.read_sql(spread_sql, conn)
        sp_pivot = sp_df.pivot_table(index="fy", columns="region", values="spread").reset_index()
        sheet    = f"Spreads_{dur}h"
        print(f" • Writing CSV: {sheet}")
        sp_pivot.to_csv(os.path.join(out_dir, f"{sheet}.csv"), index=False)

    # 8) Total Capacity Buildout
    print(" • Running Total build-out SQL…")
    tb_sql = f"""
        SELECT 
            t.year AS Year,
            tec.technology AS Technology,
            ps.subregion AS REZ,
            ypo.capacity AS capacity_in_gw,
            r.region AS State,
            p.plant AS plant,
            s.scenario AS scenario,
            CASE WHEN ps.isendogenous='1' THEN 1 ELSE 0 END AS EndogenousBuild,
            CASE 
              WHEN tec.technology IN ('baa') THEN 'Battery Storage'
              WHEN tec.technology IN ('bat_btm') THEN 'BTM Battery Storage'
              WHEN tec.technology IN ('ccg','nsg') THEN 'CCGT'
              WHEN tec.technology IN ('coa','com') THEN 'Coal'
              WHEN tec.technology IN ('dsr') THEN ''
              WHEN tec.technology IN ('ror','sto') THEN 'Hydro'
              WHEN tec.technology = 'lig' THEN 'Lignite'
              WHEN tec.technology IN ('nuc','nuk') THEN 'Nuclear'
              WHEN tec.technology IN ('ico','ocg','rge','lol_rge','lol_ico','lol_ocg') THEN 'Peaking'
              WHEN tec.technology IN ('ips','ipc') THEN 'BTM Battery Discharge'
              WHEN tec.technology = 'sol_btm' THEN 'Rooftop Solar'
              WHEN tec.technology IN ('sol','sol_csp') THEN 'Solar'
              WHEN tec.technology = 'pum' THEN 'Pumped Storage'
              WHEN tec.technology = 'won' THEN 'Wind onshore'
              WHEN tec.technology = 'wof' THEN 'Wind offshore'
              WHEN tec.technology IN ('evs','evx') THEN 'EVs'
              WHEN tec.technology = 'vpp' THEN 'BTM Battery Storage'
              WHEN tec.technology = 'h_ocgt' THEN 'Hydrogen Peaker'
              ELSE 'Other'
            END AS Tech_Classification
        FROM yearlyplantoperations ypo
        JOIN time t         ON ypo.timeid     = t.id
        JOIN scenarios s    ON ypo.scenarioid = s.id
        JOIN plants p       ON ypo.plantid    = p.id
        JOIN regions r      ON p.regionid     = r.id
        JOIN technologies tec ON p.technologyid = tec.id
        JOIN plantsettings ps ON ps.plantid=p.id AND ps.scenarioid=s.id
        WHERE s.scenario = '{scen_name}';
    """
    tb_df = pd.read_sql(tb_sql, conn)
    print(" • Writing CSV: Total_Capacity_Buildout")
    tb_df.to_csv(os.path.join(out_dir, "Total_Capacity_Buildout.csv"), index=False)

    print(f" ✓ Saved CSV files to folder: {out_dir}")

conn.close()
print("All done! Check the folder where the script lies for the outputs.")

