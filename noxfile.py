import nox


@nox.session(tags=["style", "fix"])
def black(session):
    session.install("black")
    session.run("black", "backend")


@nox.session(tags=["style", "fix"])
def isort(session):
    session.install("isort")
    session.run("isort", "backend")


@nox.session(tags=["style", "fix"])
def flake8(session):
    session.install("flake8")
    session.run("flake8", "backend")


@nox.session(tags=["style", "fix"])
def mypy(session):
    session.install("mypy")
    session.run("mypy", "backend", "--check-untyped-defs")


@nox.session(tags=["style", "fix"])
def autopep8(session):
    session.install("autopep8")
    session.run("autopep8", "backend", "--in-place", "-r")


@nox.session(tags=["test"])
def tests(session):
    """Run acceptance tests."""
    # Install dependencies for acceptance tests
    session.install("pytest", "pandas", "polars", "sqlalchemy", "psycopg2-binary", "openpyxl")

    # Run acceptance tests
    session.run("pytest", "tests/test_acceptance.py", "-v")


@nox.session(tags=["test"])
def generate_expected(session):
    """Generate expected test outputs (run this first to populate expected results)."""
    # Install dependencies
    session.install("pytest", "pandas", "polars", "sqlalchemy", "psycopg2-binary", "openpyxl")

    # Run the generate expected outputs test
    session.run("pytest", "tests/test_acceptance.py::TestAcceptance::test_generate_expected_outputs", "-v", "-s")
