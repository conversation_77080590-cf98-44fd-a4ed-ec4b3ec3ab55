# Constants required for price queries
MAX_HIGH_PRICE = 300
MIN_PRICE = 0

# Constants required for fiscal year calculations
FIRST_MONTH_FISCAL_YEAR = 7

# Column order for capacity sheets
CAPACITY_COLUMN_ORDER = [
    "fy",
    "Lignite",
    "Coal",
    "CCGT",
    "Solar",
    "Rooftop solar",
    "Wind onshore",
    "Wind offshore",
    "Hydro",
    "Pumped Storage",
    "Gas / Oil Peaker",
    "BTM Battery Storage",
    "Battery Storage",
]

# Aggregates for capacity calculations
CAPACITY_AGGREGATES = {
    "baseload": ["Lignite", "Coal", "CCGT"],
    "zero_carbon": ["Solar", "Rooftop solar", "Wind onshore", "Wind offshore", "Hydro"],
    "flexible": [
        "Pumped Storage",
        "Gas / Oil Peaker",
        "BTM Battery Storage",
        "Battery Storage",
    ],
}

# FCAS services
FCAS_SERVICES = [
    "raiseRegulation",
    "lowerRegulation",
    "raise6secContingency",
    "raiseFFR",
    "raise60secContingency",
    "raise5minContingency",
]

# Technology classification - unified mapping for all queries
TECH_CLASSIFICATION = {
    "baa": "Battery Storage",
    "bat_btm": "BTM Battery Storage",
    "ccg": "CCGT",
    "nsg": "CCGT",
    "coa": "Coal",
    "com": "Coal",
    "dsr": "",  # Empty classification, will be skipped
    "ror": "Hydro",
    "sto": "Hydro",
    "lig": "Lignite",
    "nuc": "Nuclear",
    "nuk": "Nuclear",
    "ico": "Gas / Oil Peaker",  # Updated to match Deck_Builder.py
    "ocg": "Gas / Oil Peaker",
    "rge": "Gas / Oil Peaker",
    "lol_rge": "Gas / Oil Peaker",
    "lol_ico": "Gas / Oil Peaker",
    "lol_ocg": "Gas / Oil Peaker",
    "h_ocgt": "Hydrogen Peaker",
    "ips": "BTM Battery Discharge",
    "ipc": "BTM Battery Discharge",
    "sol_btm": "Rooftop solar",  # Updated to match Deck_Builder.py
    "sol": "Solar",
    "sol_csp": "Solar",
    "pum": "Pumped Storage",
    "won": "Wind onshore",
    "wof": "Wind offshore",
    "evs": "EVs",
    "evx": "EVs",
    "vpp": "BTM Battery Storage",
}

# Note: GENERATION_TECHNOLOGY_MAPPING removed - now using TECH_CLASSIFICATION for all mappings

# Generation technologies
GENERATION_TECHNOLOGIES = [
    "Lignite",
    "Coal",
    "CCGT",
    "Solar",
    "Rooftop solar",
    "Wind onshore",
    "Wind offshore",
    "Hydro",
    "Pumped Storage",
    "Gas / Oil Peaker",
    "BTM Battery Storage",
    "Battery Storage",
]

# Generation column order
GENERATION_COLUMN_ORDER = [
    "fy",
    "Lignite",
    "Coal",
    "CCGT",
    "Solar",
    "Rooftop solar",
    "Wind onshore",
    "Wind offshore",
    "Hydro",
    "Pumped Storage",
    "Gas / Oil Peaker",
    "BTM Battery Storage",
    "Battery Storage",
]
