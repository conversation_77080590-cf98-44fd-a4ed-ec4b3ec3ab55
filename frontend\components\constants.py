"""
Frontend UI Constants

This module contains all hardcoded constants used in the frontend UI components.
Centralizing these values makes them easier to maintain and modify.
"""

# Aurora Energy Research color scheme
AURORA_COLORS = {
    'primary': '#1B365D',      # Aurora dark blue
    'secondary': '#2E5984',    # Aurora medium blue
    'accent': '#4A90A4',       # Aurora light blue
    'background': '#F8F9FA',   # Light background
    'surface': '#FFFFFF',      # White surface
    'text': '#2C3E50',         # Dark text
    'text_light': '#6C757D',   # Light text
    'success': '#28A745',      # Green
    'warning': '#FFC107',      # Yellow
    'error': '#DC3545',        # Red
    'button_primary': '#1B365D',    # Primary button color
    'button_secondary': '#4A90A4',  # Secondary button color
    'button_hover': '#2E5984',      # Button hover color
    'button_border': '#1B365D'      # Button border color
}

# Available Australian states for NEM regions
AVAILABLE_STATES = ["NSW", "VIC", "QLD", "SAA", "TAS"]

# REZ options for each state
STATE_REZS = {
    "NSW": ["N1", "N2", "N3", "N4", "N5", "N6", "N7", "N8", "N9", "N10", "N11"],
    "VIC": ["V1", "V2", "V3", "V4", "V5", "V6", "V7", "V8"],
    "QLD": ["Q1", "Q2", "Q3", "Q4", "Q5", "Q6", "Q7", "Q8", "Q9"],
    "SA": ["S1", "S2", "S3", "S4", "S5", "S6", "S7", "S8", "S9"],
    "TAS": ["T1", "T2", "T3", "T4"]
}

# Available battery duration options (in hours)
AVAILABLE_DURATIONS = [1, 2, 4, 6, 8]

# Available first year options
AVAILABLE_FIRST_YEARS = [2025, 2026, 2027, 2028, 2029, 2030]

# Default first year
DEFAULT_FIRST_YEAR = 2025

# Available generation interval options
GENERATION_INTERVAL_OPTIONS = {
    "1-year intervals": 1,
    "5-year intervals": 5
}

# Default generation interval
DEFAULT_GENERATION_INTERVAL = 5

# Default scenario name
DEFAULT_SCENARIO_NAME = "PMF AUS 25 MAY CENTRAL FINAL-FYR"

# Application window settings
WINDOW_GEOMETRY = "950x750"
WINDOW_TITLE = "Advisory Deck Builder"

# Header settings
HEADER_HEIGHT = 80
HEADER_TITLE = "Advisory Deck Builder"

# Logo settings
LOGO_SIZE = (64, 64)  # Width, Height for application icon
LOGO_PATH = "frontend/assets/logo.jpeg"

# UI spacing and padding
MAIN_PADDING = "20"
HEADER_FONT = ('Segoe UI', 16, 'bold')
LABEL_FONT = ('Segoe UI', 11, 'bold')
REGULAR_FONT = ('Segoe UI', 10)
SMALL_FONT = ('Segoe UI', 9)

# Form field settings
ENTRY_WIDTH = 50
REZ_ENTRY_WIDTH = 40
CHECKBOX_PADDING_X = 20
CHECKBOX_PADDING_Y = 5
BUTTON_PADDING_X = 15
SECTION_PADDING_Y = 15

# Configuration file name
CONFIG_FILE_NAME = "deck_builder_config.json"

# Error messages
ERROR_MESSAGES = {
    'no_scenario': "Scenario name is required",
    'no_regions': "At least one target region must be selected",
    'analysis_failed': "Failed to run analysis: {error}",
    'icon_failed': "Could not set app icon: {error}"
}

# Success messages
SUCCESS_MESSAGES = {
    'analysis_complete': "Analysis completed! Check the output folder."
}
