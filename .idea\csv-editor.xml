<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CsvFileAttributes">
    <option name="attributeMap">
      <map>
        <entry key="\outputs\AUS CP 25 Q2 ACEnergy - Vendor (Full Year Run)-FYR\Spreads_2h.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\AUS CP 25 Q2 ACEnergy - Vendor (Full Year Run)-FYR\TWA.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\AUS CP 25Q3 Project Kiwi 2.0 Central-FYR\300PricePrices.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\AUS CP 25Q3 Project Kiwi 2.0 Central-FYR\Negative_Prices.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\AUS CP 25Q3 Project Kiwi Vendor - Price Shock (2)-FYR\FCAS_Prices.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\AUS CP 25Q3 Project Kiwi Vendor - Price Shock (2)-FYR\Negative_Prices.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\AUS CP 25Q3 Project Kiwi Vendor - Price Shock (2)-FYR\Spreads_4h.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\AUS CP 25Q3 Project Kiwi Vendor - Price Shock (2)-FYR\TWA.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>