<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CsvFileAttributes">
    <option name="attributeMap">
      <map>
        <entry key="\AUS CP 25Q3 Mint -Bespoke (Price Shock Second Iteration v2)-FYR\NEM_Capacity.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\AUS CP 25Q3 Mint -Bespoke (Price Shock Second Iteration v2)-FYR\State_NSW_Capacity.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\AUS CP 25Q3 Mint -Bespoke (Price Shock Second Iteration v2)-FYR\State_QLD_Capacity.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\AUS CP 25Q3 Mint -Bespoke (Price Shock Second Iteration v2)-FYR\State_SAA_Capacity.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\AUS CP 25Q3 Mint -Bespoke (Price Shock Second Iteration v2)-FYR\State_TAS_Capacity.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\AUS CP 25Q3 Mint -Bespoke (Price Shock Second Iteration v2)-FYR\State_VIC_Capacity.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\AUS CP 25Q3 Mint -Bespoke (Price Shock Second Iteration v2)-FYR\Total_Capacity_Buildout.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\AUS CP 25Q3 Mint -Bespoke (Price Shock Second Iteration v2)-FYR\300PricePrices.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\AUS CP 25Q3 Mint -Bespoke (Price Shock Second Iteration v2)-FYR\FCAS_Prices.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\AUS CP 25Q3 Mint -Bespoke (Price Shock Second Iteration v2)-FYR\Negative_Prices.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\AUS CP 25Q3 Mint -Bespoke (Price Shock Second Iteration v2)-FYR\Spreads_2h.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\AUS CP 25Q3 Mint -Bespoke (Price Shock Second Iteration v2)-FYR\Spreads_4h.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\AUS CP 25Q3 Mint -Bespoke (Price Shock Second Iteration v2)-FYR\TWA.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\AUS CP 25Q3 Project Kiwi Vendor - Price Shock (4)-FYR\300PricePrices.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\AUS CP 25Q3 Project Kiwi Vendor - Price Shock (4)-FYR\FCAS_Prices.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\PMF AUS 25 JUL CENTRAL FINAL-FYR\300PricePrices.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\PMF AUS 25 JUL CENTRAL FINAL-FYR\FCAS_Prices.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\PMF AUS 25 JUL CENTRAL FINAL-FYR\Negative_Prices.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\PMF AUS 25 JUL CENTRAL FINAL-FYR\Spreads_4h.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
        <entry key="\outputs\PMF AUS 25 JUL CENTRAL FINAL-FYR\TWA.csv">
          <value>
            <Attribute>
              <option name="separator" value="," />
            </Attribute>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>