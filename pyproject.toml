[project]
name = "deck-builder"
version = "0.1.0"
description = "deck builder"
requires-python = ">=3.12"

dependencies = [
    'numpy',
    'pandas',  # Still needed for openpyxl integration
    'sqlalchemy>=2.0',
    'sqlalchemy-redshift',
    'redshift_connector',
    'frozendict',
    'polars',
    'psycopg2',
    'connectorx',
    'pyarrow',
    'thinkcell',
    'python-pptx',
    'pywin32',
    'pypiwin32',
    'nox',
    'pillow',
    'openpyxl'
]

[project.optional-dependencies]
dev = [
    "black>=24.1.0",
    "isort>=5.13.0",
    "mypy>=1.8.0",
    "ruff>=0.2.0",
    "pytest>=8.0.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "pytest-asyncio>=0.23.0",
    "httpx>=0.26.0",
    "pre-commit",
    "nox>=2024.0.0",
    "bandit",
    "pip-tools",
    "buildapi",
    "types-openpyxl",
    "types-SQLAlchemy"
]

[project.scripts]
main = "app:main"
