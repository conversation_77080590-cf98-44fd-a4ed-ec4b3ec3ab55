#!/usr/bin/env python3
"""
Create application icon from Aurora logo.
This script converts the logo.jpeg to a proper .ico file for Windows.
"""

from PIL import Image
import os

def create_icon():
    """Create .ico file from logo.jpeg."""
    try:
        # Load the original logo
        logo_path = "logo.jpeg"
        if not os.path.exists(logo_path):
            print("Error: logo.jpeg not found in assets folder")
            return False
        
        # Open the image
        img = Image.open(logo_path)
        
        # Convert to RGBA if not already
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        
        # Create multiple sizes for the icon (Windows standard)
        sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        
        # Create resized images
        icon_images = []
        for size in sizes:
            resized = img.resize(size, Image.Resampling.LANCZOS)
            icon_images.append(resized)
        
        # Save as .ico file
        icon_images[0].save(
            "aurora_icon.ico",
            format='ICO',
            sizes=[(img.width, img.height) for img in icon_images],
            append_images=icon_images[1:]
        )
        
        print("Successfully created aurora_icon.ico")
        return True
        
    except Exception as e:
        print(f"Error creating icon: {e}")
        return False

if __name__ == "__main__":
    create_icon()
